{"backtraceGraph": {"commands": ["install"], "files": ["src/lib/extract/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 118, "parent": 0}, {"command": 0, "file": 0, "line": 125, "parent": 0}, {"command": 0, "file": 0, "line": 130, "parent": 0}, {"command": 0, "file": 0, "line": 141, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["lib/libomop_extract.a"], "targetId": "omop_extract::@7f1d3807667a70df1022", "targetIndex": 5, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "destination": "include/omop/extract", "paths": ["src/lib/extract/csv_extractor.h", "src/lib/extract/database_connector.h", "src/lib/extract/extract.h", "src/lib/extract/extractor_base.h", "src/lib/extract/extractor_factory.h", "src/lib/extract/json_extractor.h", "src/lib/extract/postgresql_connector.h", "src/lib/extract/odbc_connector.h"], "type": "file"}, {"backtrace": 3, "component": "Unspecified", "destination": "include/omop/extract/platform", "paths": [{"from": "src/lib/extract/platform", "to": "."}], "type": "directory"}, {"backtrace": 4, "component": "Unspecified", "destination": "include/omop/extract", "paths": ["build/x86_64-release/src/lib/extract/extract_export.h"], "type": "file"}], "paths": {"build": "src/lib/extract", "source": "src/lib/extract"}}