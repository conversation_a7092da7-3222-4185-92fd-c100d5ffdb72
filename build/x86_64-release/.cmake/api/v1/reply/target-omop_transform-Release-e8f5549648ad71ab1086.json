{"archive": {}, "artifacts": [{"path": "lib/libomop_transform.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "include_directories", "target_include_directories", "target_compile_features"], "files": ["src/lib/transform/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 23, "parent": 0}, {"command": 1, "file": 0, "line": 51, "parent": 0}, {"command": 2, "file": 0, "line": 35, "parent": 0}, {"file": 1}, {"command": 3, "file": 1, "line": 477, "parent": 4}, {"command": 4, "file": 0, "line": 26, "parent": 0}, {"command": 5, "file": 0, "line": 48, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -Wpedantic -O3 -DNDEBUG -O3 -std=c++20 -arch x86_64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fPIC -fvisibility=hidden -fvisibility-inlines-hidden"}], "defines": [{"backtrace": 3, "define": "FMT_SHARED"}, {"backtrace": 3, "define": "OMOP_HAS_ODBC"}, {"backtrace": 3, "define": "SPDLOG_COMPILED_LIB"}, {"backtrace": 3, "define": "SPDLOG_SHARED_LIB"}], "includes": [{"backtrace": 5, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib"}, {"backtrace": 5, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/include"}, {"backtrace": 5, "path": "/opt/homebrew/opt/postgresql@15/include"}, {"backtrace": 5, "path": "/opt/homebrew/opt/postgresql@15/include/postgresql/server"}, {"backtrace": 5, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/cpp_httplib-src"}, {"backtrace": 6, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform/.."}, {"backtrace": 6, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/transform"}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/core/.."}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/common/.."}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/spdlog-src/include"}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/src/lib/extract/.."}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/nlo<PERSON>_json-src/include"}, {"backtrace": 3, "path": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release/_deps/fmt-src/include"}, {"backtrace": 3, "isSystem": true, "path": "/opt/homebrew/include"}], "language": "CXX", "languageStandard": {"backtraces": [7, 3], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "dependencies": [{"backtrace": 3, "id": "fmt::@976f4f0bee90b99ecdb6"}, {"backtrace": 3, "id": "spdlog::@eb35cfce7893ccfeff1e"}, {"backtrace": 3, "id": "omop_common::@a72075f8f48ba5338622"}, {"backtrace": 3, "id": "omop_core::@a0f0ea145c6a42f32922"}, {"backtrace": 3, "id": "omop_extract::@7f1d3807667a70df1022"}], "id": "omop_transform::@3802e2df5bc6278e8201", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "/Users/<USER>/uclwork/etl/omop-etl/install/x86_64-release"}}, "name": "omop_transform", "nameOnDisk": "libomop_transform.a", "paths": {"build": "src/lib/transform", "source": "src/lib/transform"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/transform/conditional_transformations.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/transform/custom_transformations.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/transform/date_transformations.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/transform/field_transformations.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/transform/numeric_transformations.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/transform/string_transformations.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/transform/transformation_engine.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/transform/validation_engine.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/transform/vocabulary_service.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/lib/transform/vocabulary_transformations.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}