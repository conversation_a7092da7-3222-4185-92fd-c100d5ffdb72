{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5], "hasInstallRule": true, "jsonFile": "directory-.-Release-2b1601b15aecc391ad67.json", "minimumCMakeVersion": {"string": "3.23"}, "projectIndex": 0, "source": ".", "targetIndexes": [1]}, {"build": "_deps/fmt-build", "hasInstallRule": true, "jsonFile": "directory-_deps.fmt-build-Release-c62eab2ac4206b180f32.json", "minimumCMakeVersion": {"string": "3.8"}, "parentIndex": 0, "projectIndex": 1, "source": "build/x86_64-release/_deps/fmt-src", "targetIndexes": [0]}, {"build": "_deps/n<PERSON><PERSON>_json-build", "jsonFile": "directory-_deps.n<PERSON><PERSON>_json-build-Release-cc6cfb2d9e59f8465bd5.json", "minimumCMakeVersion": {"string": "3.1"}, "parentIndex": 0, "projectIndex": 2, "source": "build/x86_64-release/_deps/nlohmann_json-src"}, {"build": "_deps/spdlog-build", "jsonFile": "directory-_deps.spdlog-build-Release-b7eadc6e01b6a2c33303.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 0, "projectIndex": 3, "source": "build/x86_64-release/_deps/spdlog-src", "targetIndexes": [9]}, {"build": "_deps/cpp_httplib-build", "hasInstallRule": true, "jsonFile": "directory-_deps.cpp_httplib-build-Release-ec4ac50031eb6064f1c7.json", "minimumCMakeVersion": {"string": "3.14.0"}, "parentIndex": 0, "projectIndex": 4, "source": "build/x86_64-release/_deps/cpp_httplib-src"}, {"build": "src", "childIndexes": [6], "hasInstallRule": true, "jsonFile": "directory-src-Release-b6cf2213fc5053b67c22.json", "minimumCMakeVersion": {"string": "3.23"}, "parentIndex": 0, "projectIndex": 0, "source": "src"}, {"build": "src/lib", "childIndexes": [7, 8, 9, 10, 11, 12, 13], "hasInstallRule": true, "jsonFile": "directory-src.lib-Release-0007d279bd133a371b88.json", "minimumCMakeVersion": {"string": "3.23"}, "parentIndex": 5, "projectIndex": 0, "source": "src/lib"}, {"build": "src/lib/common", "hasInstallRule": true, "jsonFile": "directory-src.lib.common-Release-90717b0591bb41c5f3b2.json", "minimumCMakeVersion": {"string": "3.23"}, "parentIndex": 6, "projectIndex": 0, "source": "src/lib/common", "targetIndexes": [3]}, {"build": "src/lib/core", "hasInstallRule": true, "jsonFile": "directory-src.lib.core-Release-3576a1ff597794bf5d09.json", "minimumCMakeVersion": {"string": "3.23"}, "parentIndex": 6, "projectIndex": 0, "source": "src/lib/core", "targetIndexes": [4]}, {"build": "src/lib/cdm", "hasInstallRule": true, "jsonFile": "directory-src.lib.cdm-Release-1be723b417e5dae70ab2.json", "minimumCMakeVersion": {"string": "3.23"}, "parentIndex": 6, "projectIndex": 0, "source": "src/lib/cdm", "targetIndexes": [2]}, {"build": "src/lib/extract", "hasInstallRule": true, "jsonFile": "directory-src.lib.extract-Release-b9d6bc15c355cc26a07e.json", "minimumCMakeVersion": {"string": "3.23"}, "parentIndex": 6, "projectIndex": 0, "source": "src/lib/extract", "targetIndexes": [5]}, {"build": "src/lib/transform", "hasInstallRule": true, "jsonFile": "directory-src.lib.transform-Release-ace2677ca8101b541d95.json", "minimumCMakeVersion": {"string": "3.23"}, "parentIndex": 6, "projectIndex": 0, "source": "src/lib/transform", "targetIndexes": [8]}, {"build": "src/lib/load", "hasInstallRule": true, "jsonFile": "directory-src.lib.load-Release-626f636ac62853a819f5.json", "minimumCMakeVersion": {"string": "3.23"}, "parentIndex": 6, "projectIndex": 0, "source": "src/lib/load", "targetIndexes": [6]}, {"build": "src/lib/service", "hasInstallRule": true, "jsonFile": "directory-src.lib.service-Release-6b20d3bf1e4ab116244b.json", "minimumCMakeVersion": {"string": "3.23"}, "parentIndex": 6, "projectIndex": 0, "source": "src/lib/service", "targetIndexes": [7]}], "name": "Release", "projects": [{"childIndexes": [1, 2, 3, 4], "directoryIndexes": [0, 5, 6, 7, 8, 9, 10, 11, 12, 13], "name": "omop_etl", "targetIndexes": [1, 2, 3, 4, 5, 6, 7, 8]}, {"directoryIndexes": [1], "name": "FMT", "parentIndex": 0, "targetIndexes": [0]}, {"directoryIndexes": [2], "name": "n<PERSON><PERSON>_json", "parentIndex": 0}, {"directoryIndexes": [3], "name": "spdlog", "parentIndex": 0, "targetIndexes": [9]}, {"directoryIndexes": [4], "name": "httplib", "parentIndex": 0}], "targets": [{"directoryIndex": 1, "id": "fmt::@976f4f0bee90b99ecdb6", "jsonFile": "target-fmt-Release-3908a0647bbeaa976880.json", "name": "fmt", "projectIndex": 1}, {"directoryIndex": 0, "id": "generate_sql_files::@6890427a1f51a3e7e1df", "jsonFile": "target-generate_sql_files-Release-d3995a3c0bc16e612520.json", "name": "generate_sql_files", "projectIndex": 0}, {"directoryIndex": 9, "id": "omop_cdm::@3089087819f5751d3e23", "jsonFile": "target-omop_cdm-Release-9dc39f9b2c8e03753e36.json", "name": "omop_cdm", "projectIndex": 0}, {"directoryIndex": 7, "id": "omop_common::@a72075f8f48ba5338622", "jsonFile": "target-omop_common-Release-08e902409afab47890c4.json", "name": "omop_common", "projectIndex": 0}, {"directoryIndex": 8, "id": "omop_core::@a0f0ea145c6a42f32922", "jsonFile": "target-omop_core-Release-aba3d725c3fbdc392a8c.json", "name": "omop_core", "projectIndex": 0}, {"directoryIndex": 10, "id": "omop_extract::@7f1d3807667a70df1022", "jsonFile": "target-omop_extract-Release-5d946a6ec48c1d6da0e5.json", "name": "omop_extract", "projectIndex": 0}, {"directoryIndex": 12, "id": "omop_load::@f951d3c1accc31f84506", "jsonFile": "target-omop_load-Release-177226c3d1a705535c2b.json", "name": "omop_load", "projectIndex": 0}, {"directoryIndex": 13, "id": "omop_service::@bd0a1e95ba332999caf6", "jsonFile": "target-omop_service-Release-4d016cb5c4de6fbee2d0.json", "name": "omop_service", "projectIndex": 0}, {"directoryIndex": 11, "id": "omop_transform::@3802e2df5bc6278e8201", "jsonFile": "target-omop_transform-Release-e8f5549648ad71ab1086.json", "name": "omop_transform", "projectIndex": 0}, {"directoryIndex": 3, "id": "spdlog::@eb35cfce7893ccfeff1e", "jsonFile": "target-spdlog-Release-46b6546d63916c9b4e31.json", "name": "spdlog", "projectIndex": 3}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/uclwork/etl/omop-etl/build/x86_64-release", "source": "/Users/<USER>/uclwork/etl/omop-etl"}, "version": {"major": 2, "minor": 7}}