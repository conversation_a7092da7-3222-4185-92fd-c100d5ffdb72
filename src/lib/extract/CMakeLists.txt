# Extract library CMakeLists.txt
# OMOP ETL Extract Module Build Configuration

# Define source files
set(EXTRACT_SOURCES
    connection_pool.cpp
    compressed_csv_extractor.cpp
    csv_extractor.cpp
    extract_utils.cpp
    database_connector.cpp
    extractor_base.cpp
    extractor_factory.cpp
    json_extractor.cpp
    postgresql_connector.cpp
)

# Define header files
set(EXTRACT_HEADERS
    csv_extractor.h
    database_connector.h
    extract.h
    extractor_base.h
    extractor_factory.h
    json_extractor.h
    postgresql_connector.h
)

# Platform-specific sources
if(WIN32)
    list(APPEND EXTRACT_SOURCES platform/windows_utils.cpp)
elseif(UNIX)
    list(APPEND EXTRACT_SOURCES platform/unix_utils.cpp)
endif()

# MySQL support
find_package(MySQL)
if(MySQL_FOUND)
    list(APPEND EXTRACT_SOURCES mysql_connector.cpp)
    list(APPEND EXTRACT_HEADERS mysql_connector.h)
endif()

# ODBC support
find_package(ODBC)
if(ODBC_FOUND)
    list(APPEND EXTRACT_SOURCES odbc_connector.cpp)
    list(APPEND EXTRACT_HEADERS odbc_connector.h)
endif()

# Create extract library
add_library(omop_extract STATIC ${EXTRACT_SOURCES})

# Set include directories
target_include_directories(omop_extract
    PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${PostgreSQL_INCLUDE_DIRS}
)

# Configure MySQL if found
if(MySQL_FOUND)
    target_include_directories(omop_extract PRIVATE ${MySQL_INCLUDE_DIRS})
    target_link_libraries(omop_extract PRIVATE ${MySQL_LIBRARIES})
    target_compile_definitions(omop_extract PRIVATE OMOP_HAS_MYSQL)
endif()

# Configure ODBC if found
if(ODBC_FOUND)
    target_include_directories(omop_extract PRIVATE ${ODBC_INCLUDE_DIRS})
    target_link_libraries(omop_extract PRIVATE ${ODBC_LIBRARIES})
    target_compile_definitions(omop_extract PUBLIC OMOP_HAS_ODBC)
endif()

# Link dependencies
target_link_libraries(omop_extract
    PUBLIC
        omop_core
        omop_common
        nlohmann_json::nlohmann_json
    PRIVATE
        PostgreSQL::PostgreSQL
        ZLIB::ZLIB
        Threads::Threads
        $<$<BOOL:${HAVE_LIBARCHIVE}>:LibArchive::LibArchive>
)

# Link MySQL if found
if(TARGET MySQL::MySQL)
    target_link_libraries(omop_extract PRIVATE MySQL::MySQL)
    target_compile_definitions(omop_extract PRIVATE OMOP_HAS_MYSQL)
endif()

# Set compile features and flags
target_compile_features(omop_extract PUBLIC cxx_std_20)

# Platform-specific compile options
if(MSVC)
    target_compile_options(omop_extract PRIVATE /W4 /WX)
else()
    target_compile_options(omop_extract PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Enable position independent code
set_target_properties(omop_extract PROPERTIES POSITION_INDEPENDENT_CODE ON)

# Export compile commands for IDE support
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Installation rules
install(TARGETS omop_extract
    EXPORT omop-etl-targets
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${EXTRACT_HEADERS}
    DESTINATION include/omop/extract
)

# Install platform-specific headers
install(DIRECTORY platform/
    DESTINATION include/omop/extract/platform
    FILES_MATCHING PATTERN "*.h"
)

# Generate export header
include(GenerateExportHeader)
generate_export_header(omop_extract
    EXPORT_FILE_NAME ${CMAKE_CURRENT_BINARY_DIR}/extract_export.h
)

install(FILES ${CMAKE_CURRENT_BINARY_DIR}/extract_export.h
    DESTINATION include/omop/extract
)
