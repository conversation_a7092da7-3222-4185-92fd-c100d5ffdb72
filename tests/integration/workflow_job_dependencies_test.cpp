// Test job dependency management in complex ETL workflows
#include <gtest/gtest.h>
#include "core/job_manager.h"
#include "core/job_scheduler.h"
#include "core/pipeline.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"
#include <chrono>
#include <thread>

namespace omop::test::integration::workflow {

using namespace omop::core;
using namespace std::chrono_literals;

class JobDependencyTest : public IntegrationTestBase, public DatabaseFixture {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        DatabaseFixture::SetUp();
        
        // Initialize job manager with test configuration
        auto config = std::make_shared<common::ConfigurationManager>();
        config->load_config_from_string(R"(
            etl:
              max_concurrent_jobs: 4
              job_timeout_seconds: 300
              enable_checkpointing: true
            database:
              source:
                type: postgresql
                host: localhost
                port: 5432
                database: test_source
              target:
                type: postgresql
                host: localhost
                port: 5432
                database: test_omop
        )");
        
        job_manager_ = std::make_unique<JobManager>(config, logger_);
        job_manager_->start();
        
        scheduler_ = std::make_unique<JobScheduler>(job_manager_);
        scheduler_->start();
    }
    
    void TearDown() override {
        scheduler_->stop();
        job_manager_->stop();
        DatabaseFixture::TearDown();
        IntegrationTestBase::TearDown();
    }
    
    JobConfig create_test_job_config(const std::string& name, 
                                   const std::vector<std::string>& dependencies = {}) {
        JobConfig config;
        config.job_name = name;
        config.job_id = generate_unique_id(name);
        config.pipeline_config_path = "test_pipeline.yaml";
        config.priority = JobPriority::NORMAL;
        config.parameters["table"] = name;
        
        // Add dependencies
        for (const auto& dep : dependencies) {
            config.parameters["depends_on"] = dep;
        }
        
        return config;
    }
    
    std::string submit_test_job(const std::string& name,
                              const std::vector<std::string>& dependencies = {}) {
        auto config = create_test_job_config(name, dependencies);
        return scheduler_->submitJob(config, JobPriority::NORMAL, dependencies);
    }
    
protected:
    std::unique_ptr<JobManager> job_manager_;
    std::unique_ptr<JobScheduler> scheduler_;
    std::map<std::string, std::string> job_name_to_id_;
};

TEST_F(JobDependencyTest, SimpleLinearDependencyChain) {
    // Test linear dependency chain: A -> B -> C -> D
    std::vector<std::string> job_ids;
    std::vector<std::string> job_names = {"job_A", "job_B", "job_C", "job_D"};
    
    // Submit jobs with dependencies
    std::string prev_id;
    for (size_t i = 0; i < job_names.size(); ++i) {
        std::vector<std::string> deps;
        if (i > 0) {
            deps.push_back(prev_id);
        }
        
        auto job_id = submit_test_job(job_names[i], deps);
        job_ids.push_back(job_id);
        job_name_to_id_[job_names[i]] = job_id;
        prev_id = job_id;
    }
    
    // Wait for all jobs to complete
    for (const auto& job_id : job_ids) {
        ASSERT_TRUE(job_manager_->wait_for_job(job_id, 30000)); // 30 second timeout
    }
    
    // Verify execution order
    std::vector<std::chrono::system_clock::time_point> start_times;
    for (const auto& job_id : job_ids) {
        auto job = job_manager_->getJob(job_id);
        ASSERT_NE(job, nullptr);
        EXPECT_EQ(job->getStatus(), JobStatus::Completed);
        start_times.push_back(job->getStartTime());
    }
    
    // Verify jobs started in correct order
    for (size_t i = 1; i < start_times.size(); ++i) {
        EXPECT_GE(start_times[i], start_times[i-1]);
    }
}

TEST_F(JobDependencyTest, ComplexDependencyDAG) {
    // Test complex DAG:
    //     A
    //    / \
    //   B   C
    //   |\ /|
    //   | X |
    //   |/ \|
    //   D   E
    //    \ /
    //     F
    
    // Submit job A (no dependencies)
    auto job_a = submit_test_job("job_A");
    
    // Submit jobs B and C (depend on A)
    auto job_b = submit_test_job("job_B", {job_a});
    auto job_c = submit_test_job("job_C", {job_a});
    
    // Submit jobs D and E (depend on both B and C)
    auto job_d = submit_test_job("job_D", {job_b, job_c});
    auto job_e = submit_test_job("job_E", {job_b, job_c});
    
    // Submit job F (depends on D and E)
    auto job_f = submit_test_job("job_F", {job_d, job_e});
    
    // Wait for final job to complete
    ASSERT_TRUE(job_manager_->wait_for_job(job_f, 60000)); // 60 second timeout
    
    // Verify all jobs completed successfully
    std::vector<std::string> all_jobs = {job_a, job_b, job_c, job_d, job_e, job_f};
    for (const auto& job_id : all_jobs) {
        auto job = job_manager_->getJob(job_id);
        ASSERT_NE(job, nullptr);
        EXPECT_EQ(job->getStatus(), JobStatus::Completed);
    }
    
    // Verify dependency constraints were respected
    auto get_start_time = [this](const std::string& job_id) {
        return job_manager_->getJob(job_id)->getStartTime();
    };
    
    // A should start first
    auto time_a = get_start_time(job_a);
    
    // B and C should start after A
    auto time_b = get_start_time(job_b);
    auto time_c = get_start_time(job_c);
    EXPECT_GT(time_b, time_a);
    EXPECT_GT(time_c, time_a);
    
    // D and E should start after both B and C
    auto time_d = get_start_time(job_d);
    auto time_e = get_start_time(job_e);
    EXPECT_GT(time_d, time_b);
    EXPECT_GT(time_d, time_c);
    EXPECT_GT(time_e, time_b);
    EXPECT_GT(time_e, time_c);
    
    // F should start after both D and E
    auto time_f = get_start_time(job_f);
    EXPECT_GT(time_f, time_d);
    EXPECT_GT(time_f, time_e);
}

TEST_F(JobDependencyTest, CircularDependencyDetection) {
    // Test circular dependency detection: A -> B -> C -> A
    auto job_a = submit_test_job("job_A");
    auto job_b = submit_test_job("job_B", {job_a});
    auto job_c = submit_test_job("job_C", {job_b});
    
    // Try to add circular dependency
    EXPECT_THROW({
        scheduler_->addDependency(job_a, job_c);
    }, std::runtime_error);
}

TEST_F(JobDependencyTest, DependencyFailurePropagation) {
    // Test that dependent jobs are cancelled when dependency fails
    
    // Create a job that will fail
    JobConfig failing_config;
    failing_config.job_name = "failing_job";
    failing_config.job_id = generate_unique_id("failing_job");
    failing_config.pipeline_config_path = "invalid_pipeline.yaml"; // This will cause failure
    failing_config.priority = JobPriority::NORMAL;
    
    auto failing_job = job_manager_->submitJob(failing_config);
    
    // Submit dependent jobs
    auto dependent1 = submit_test_job("dependent1", {failing_job});
    auto dependent2 = submit_test_job("dependent2", {failing_job});
    auto dependent3 = submit_test_job("dependent3", {dependent1, dependent2});
    
    // Wait for failing job to fail
    std::this_thread::sleep_for(2s);
    
    // Verify failing job failed
    auto job = job_manager_->getJob(failing_job);
    ASSERT_NE(job, nullptr);
    EXPECT_EQ(job->getStatus(), JobStatus::Failed);
    
    // Verify dependent jobs were cancelled
    std::vector<std::string> dependents = {dependent1, dependent2, dependent3};
    for (const auto& job_id : dependents) {
        auto dep_job = job_manager_->getJob(job_id);
        ASSERT_NE(dep_job, nullptr);
        EXPECT_TRUE(dep_job->getStatus() == JobStatus::Cancelled ||
                   dep_job->getStatus() == JobStatus::Created);
    }
}

TEST_F(JobDependencyTest, ParallelExecutionWithSharedDependencies) {
    // Test parallel execution of jobs with shared dependencies
    //     A
    //   / | \
    //  B  C  D  (should run in parallel)
    //   \ | /
    //     E
    
    auto job_a = submit_test_job("job_A");
    
    // Submit parallel jobs
    auto job_b = submit_test_job("job_B", {job_a});
    auto job_c = submit_test_job("job_C", {job_a});
    auto job_d = submit_test_job("job_D", {job_a});
    
    auto job_e = submit_test_job("job_E", {job_b, job_c, job_d});
    
    // Wait for completion
    ASSERT_TRUE(job_manager_->wait_for_job(job_e, 30000));
    
    // Verify parallel execution
    auto get_times = [this](const std::string& job_id) {
        auto job = job_manager_->getJob(job_id);
        return std::make_pair(job->getStartTime(), job->getEndTime());
    };
    
    auto [b_start, b_end] = get_times(job_b);
    auto [c_start, c_end] = get_times(job_c);
    auto [d_start, d_end] = get_times(job_d);
    
    // Check for overlap in execution times (indicating parallel execution)
    bool b_c_overlap = (b_start < c_end) && (c_start < b_end);
    bool b_d_overlap = (b_start < d_end) && (d_start < b_end);
    bool c_d_overlap = (c_start < d_end) && (d_start < c_end);
    
    // At least some jobs should have overlapped
    EXPECT_TRUE(b_c_overlap || b_d_overlap || c_d_overlap);
}

TEST_F(JobDependencyTest, DynamicDependencyAddition) {
    // Test adding dependencies after job submission
    auto job_a = submit_test_job("job_A");
    auto job_b = submit_test_job("job_B");
    auto job_c = submit_test_job("job_C");
    
    // Add dependencies dynamically
    scheduler_->addDependency(job_b, job_a);
    scheduler_->addDependency(job_c, job_b);
    
    // Wait for completion
    ASSERT_TRUE(job_manager_->wait_for_job(job_c, 30000));
    
    // Verify execution order
    auto time_a = job_manager_->getJob(job_a)->getStartTime();
    auto time_b = job_manager_->getJob(job_b)->getStartTime();
    auto time_c = job_manager_->getJob(job_c)->getStartTime();
    
    EXPECT_LT(time_a, time_b);
    EXPECT_LT(time_b, time_c);
}

TEST_F(JobDependencyTest, ConditionalDependencyExecution) {
    // Test conditional dependency based on job output
    JobConfig conditional_job;
    conditional_job.job_name = "conditional_job";
    conditional_job.job_id = generate_unique_id("conditional_job");
    conditional_job.pipeline_config_path = "conditional_pipeline.yaml";
    conditional_job.parameters["condition"] = "check_data_quality";
    
    auto cond_job_id = job_manager_->submitJob(conditional_job);
    
    // Submit jobs that depend on condition
    JobConfig success_path;
    success_path.job_name = "success_path";
    success_path.job_id = generate_unique_id("success_path");
    success_path.pipeline_config_path = "success_pipeline.yaml";
    success_path.parameters["depends_on_success"] = cond_job_id;
    
    JobConfig failure_path;
    failure_path.job_name = "failure_path";
    failure_path.job_id = generate_unique_id("failure_path");
    failure_path.pipeline_config_path = "failure_pipeline.yaml";
    failure_path.parameters["depends_on_failure"] = cond_job_id;
    
    auto success_job_id = scheduler_->submitConditionalJob(success_path, 
        {cond_job_id}, JobCondition::OnSuccess);
    auto failure_job_id = scheduler_->submitConditionalJob(failure_path, 
        {cond_job_id}, JobCondition::OnFailure);
    
    // Wait for conditional job to complete
    ASSERT_TRUE(job_manager_->wait_for_job(cond_job_id, 30000));
    
    // Check which path was taken based on conditional job result
    auto cond_job = job_manager_->getJob(cond_job_id);
    if (cond_job->getStatus() == JobStatus::Completed) {
        // Success path should run
        ASSERT_TRUE(job_manager_->wait_for_job(success_job_id, 30000));
        auto success_job = job_manager_->getJob(success_job_id);
        EXPECT_EQ(success_job->getStatus(), JobStatus::Completed);
        
        // Failure path should not run
        auto failure_job = job_manager_->getJob(failure_job_id);
        EXPECT_EQ(failure_job->getStatus(), JobStatus::Cancelled);
    }
}

} // namespace omop::test::integration::workflow