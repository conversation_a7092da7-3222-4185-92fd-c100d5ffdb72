// Test workflow failure recovery and checkpointing mechanisms
#include <gtest/gtest.h>
#include "core/job_manager.h"
#include "core/job_scheduler.h"
#include "core/pipeline.h"
#include "service/etl_service.h"
#include "test_helpers/integration_test_base.h"
#include "test_helpers/database_fixture.h"
#include <filesystem>
#include <fstream>

namespace omop::test::integration::workflow {

using namespace omop::core;
using namespace omop::service;
using namespace std::chrono_literals;

class WorkflowRecoveryTest : public IntegrationTestBase, public DatabaseFixture {
protected:
    void SetUp() override {
        IntegrationTestBase::SetUp();
        DatabaseFixture::SetUp();
        
        // Set up checkpoint directory
        checkpoint_dir_ = std::filesystem::temp_directory_path() / "omop_test_checkpoints";
        std::filesystem::create_directories(checkpoint_dir_);
        
        auto config = create_test_config();
        config->get_etl_settings()["checkpoint_dir"] = checkpoint_dir_.string();
        config->get_etl_settings()["enable_checkpointing"] = true;
        config->get_etl_settings()["checkpoint_interval"] = 100; // Every 100 records
        
        job_manager_ = std::make_unique<JobManager>(config, logger_);
        job_manager_->start();
        
        scheduler_ = std::make_unique<JobScheduler>(job_manager_);
        scheduler_->start();
        
        pipeline_manager_ = std::make_shared<PipelineManager>(4);
        etl_service_ = std::make_unique<ETLService>(config, pipeline_manager_);
        
        create_recovery_tables();
    }
    
    void TearDown() override {
        scheduler_->stop();
        job_manager_->stop();
        pipeline_manager_->shutdown();
        
        // Clean up checkpoint directory
        std::filesystem::remove_all(checkpoint_dir_);
        
        DatabaseFixture::TearDown();
        IntegrationTestBase::TearDown();
    }
    
    void create_recovery_tables() {
        execute_sql(R"(
            CREATE TABLE IF NOT EXISTS job_checkpoints (
                job_id VARCHAR(100) PRIMARY KEY,
                checkpoint_data JSONB,
                last_processed_id INTEGER,
                last_processed_timestamp TIMESTAMP,
                records_processed INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )");
        
        execute_sql(R"(
            CREATE TABLE IF NOT EXISTS recovery_test_source (
                id SERIAL PRIMARY KEY,
                data_value VARCHAR(255),
                processing_flag BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )");
        
        execute_sql(R"(
            CREATE TABLE IF NOT EXISTS recovery_test_target (
                id SERIAL PRIMARY KEY,
                source_id INTEGER,
                processed_value VARCHAR(255),
                job_id VARCHAR(100),
                loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )");
        
        execute_sql(R"(
            CREATE TABLE IF NOT EXISTS job_recovery_log (
                id SERIAL PRIMARY KEY,
                job_id VARCHAR(100),
                recovery_type VARCHAR(50),
                records_recovered INTEGER,
                recovery_point TIMESTAMP,
                success BOOLEAN,
                error_message TEXT,
                recovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )");
    }
    
    void populate_test_data(size_t count) {
        for (size_t i = 0; i < count; ++i) {
            execute_sql(std::format(
                "INSERT INTO recovery_test_source (data_value) VALUES ('test_value_{}')",
                i
            ));
        }
    }
    
    void simulate_job_failure(const std::string& job_id, size_t after_records) {
        // Simulate processing up to a certain point
        auto records = execute_query<std::tuple<int, std::string>>(
            "SELECT id, data_value FROM recovery_test_source ORDER BY id LIMIT " + 
            std::to_string(after_records)
        );
        
        for (const auto& [id, value] : records) {
            execute_sql(std::format(
                "INSERT INTO recovery_test_target (source_id, processed_value, job_id) "
                "VALUES ({}, '{}', '{}')",
                id, value, job_id
            ));
            
            execute_sql(std::format(
                "UPDATE recovery_test_source SET processing_flag = TRUE WHERE id = {}",
                id
            ));
        }
        
        // Create checkpoint
        execute_sql(std::format(
            "INSERT INTO job_checkpoints (job_id, checkpoint_data, last_processed_id, "
            "last_processed_timestamp, records_processed) "
            "VALUES ('{}', '{}', {}, CURRENT_TIMESTAMP, {})",
            job_id,
            R"({"stage": "processing", "batch_number": 2, "error_count": 0})",
            records.back().get<0>(),
            after_records
        ));
        
        // Also create file-based checkpoint
        auto checkpoint_file = checkpoint_dir_ / (job_id + ".checkpoint");
        std::ofstream ofs(checkpoint_file);
        nlohmann::json checkpoint = {
            {"job_id", job_id},
            {"last_processed_id", records.back().get<0>()},
            {"records_processed", after_records},
            {"timestamp", std::chrono::system_clock::now().time_since_epoch().count()}
        };
        ofs << checkpoint.dump();
    }
    
protected:
    std::unique_ptr<JobManager> job_manager_;
    std::unique_ptr<JobScheduler> scheduler_;
    std::shared_ptr<PipelineManager> pipeline_manager_;
    std::unique_ptr<ETLService> etl_service_;
    std::filesystem::path checkpoint_dir_;
};

TEST_F(WorkflowRecoveryTest, BasicCheckpointRecovery) {
    // Test basic checkpoint and recovery functionality
    populate_test_data(1000);
    
    // Create a job that will fail after 300 records
    ETLJobRequest request;
    request.name = "checkpoint_test_job";
    request.source_table = "recovery_test_source";
    request.target_table = "recovery_test_target";
    request.pipeline_config.batch_size = 100;
    request.pipeline_config.checkpoint_interval = 100s;
    
    auto job_id = etl_service_->create_job(request);
    
    // Simulate failure after 300 records
    simulate_job_failure(job_id, 300);
    
    // Mark job as failed
    auto job = job_manager_->getJob(job_id);
    job->setStatus(JobStatus::Failed);
    job->setErrorMessage("Simulated failure for testing");
    
    // Attempt recovery
    ASSERT_TRUE(job_manager_->retryJob(job_id));
    
    // Wait for recovery to complete
    ASSERT_TRUE(job_manager_->wait_for_job(job_id, 30000));
    
    // Verify recovery
    auto final_status = job_manager_->getJob(job_id)->getStatus();
    EXPECT_EQ(final_status, JobStatus::Completed);
    
    // Check that all records were processed
    auto total_processed = execute_scalar<int>(
        std::format("SELECT COUNT(*) FROM recovery_test_target WHERE job_id = '{}'", job_id)
    );
    EXPECT_EQ(total_processed, 1000);
    
    // Verify no duplicate processing
    auto duplicates = execute_scalar<int>(
        "SELECT COUNT(*) FROM recovery_test_target "
        "GROUP BY source_id HAVING COUNT(*) > 1"
    );
    EXPECT_EQ(duplicates, 0);
    
    // Check recovery log
    auto recovery_log = execute_query<std::tuple<std::string, int, bool>>(
        std::format("SELECT recovery_type, records_recovered, success "
                   "FROM job_recovery_log WHERE job_id = '{}'", job_id)
    );
    
    ASSERT_FALSE(recovery_log.empty());
    auto [recovery_type, records_recovered, success] = recovery_log[0];
    EXPECT_EQ(recovery_type, "checkpoint");
    EXPECT_EQ(records_recovered, 700); // 1000 - 300 already processed
    EXPECT_TRUE(success);
}

TEST_F(WorkflowRecoveryTest, MultiStageWorkflowRecovery) {
    // Test recovery of multi-stage workflow with failures at different stages
    
    // Create staging table
    execute_sql(R"(
        CREATE TABLE IF NOT EXISTS recovery_test_staging (
            id SERIAL PRIMARY KEY,
            source_id INTEGER,
            transformed_value VARCHAR(255),
            validation_status VARCHAR(50),
            stage_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    )");
    
    populate_test_data(500);
    
    // Stage 1: Extract to staging
    ETLJobRequest stage1_request;
    stage1_request.name = "extract_to_staging";
    stage1_request.source_table = "recovery_test_source";
    stage1_request.target_table = "recovery_test_staging";
    
    auto stage1_job_id = etl_service_->create_job(stage1_request);
    
    // Simulate partial completion of stage 1
    simulate_job_failure(stage1_job_id, 200);
    
    // Stage 2: Transform and load (dependent on stage 1)
    ETLJobRequest stage2_request;
    stage2_request.name = "transform_and_load";
    stage2_request.source_table = "recovery_test_staging";
    stage2_request.target_table = "recovery_test_target";
    
    auto stage2_job_id = scheduler_->submitJob(
        create_job_config(stage2_request),
        JobPriority::NORMAL,
        {stage1_job_id}
    );
    
    // Recover stage 1
    ASSERT_TRUE(job_manager_->retryJob(stage1_job_id));
    
    // Wait for both stages to complete
    ASSERT_TRUE(job_manager_->wait_for_job(stage1_job_id, 30000));
    ASSERT_TRUE(job_manager_->wait_for_job(stage2_job_id, 30000));
    
    // Verify both stages completed successfully
    EXPECT_EQ(job_manager_->getJob(stage1_job_id)->getStatus(), JobStatus::Completed);
    EXPECT_EQ(job_manager_->getJob(stage2_job_id)->getStatus(), JobStatus::Completed);
    
    // Verify data integrity across stages
    auto staging_count = execute_scalar<int>(
        "SELECT COUNT(*) FROM recovery_test_staging"
    );
    auto target_count = execute_scalar<int>(
        "SELECT COUNT(*) FROM recovery_test_target"
    );
    
    EXPECT_EQ(staging_count, 500);
    EXPECT_GT(target_count, 0);
}

TEST_F(WorkflowRecoveryTest, ParallelJobRecovery) {
    // Test recovery of parallel jobs with partial failures
    
    // Create multiple source tables
    for (int i = 1; i <= 3; ++i) {
        execute_sql(std::format(R"(
            CREATE TABLE IF NOT EXISTS recovery_test_source_{} (
                id SERIAL PRIMARY KEY,
                data_value VARCHAR(255),
                batch_id INTEGER DEFAULT {}
            )
        )", i, i));
        
        // Populate each table
        for (int j = 0; j < 300; ++j) {
            execute_sql(std::format(
                "INSERT INTO recovery_test_source_{} (data_value) "
                "VALUES ('batch_{}_value_{}')",
                i, i, j
            ));
        }
    }
    
    // Submit parallel jobs
    std::vector<std::string> job_ids;
    for (int i = 1; i <= 3; ++i) {
        ETLJobRequest request;
        request.name = std::format("parallel_job_{}", i);
        request.source_table = std::format("recovery_test_source_{}", i);
        request.target_table = "recovery_test_target";
        
        auto job_id = etl_service_->create_job(request);
        job_ids.push_back(job_id);
    }
    
    // Simulate failures at different points
    simulate_job_failure(job_ids[0], 100); // Job 1 fails after 100 records
    simulate_job_failure(job_ids[1], 200); // Job 2 fails after 200 records
    // Job 3 runs to completion
    
    // Mark failed jobs
    job_manager_->getJob(job_ids[0])->setStatus(JobStatus::Failed);
    job_manager_->getJob(job_ids[1])->setStatus(JobStatus::Failed);
    
    // Recover failed jobs
    ASSERT_TRUE(job_manager_->retryJob(job_ids[0]));
    ASSERT_TRUE(job_manager_->retryJob(job_ids[1]));
    
    // Wait for all jobs
    for (const auto& job_id : job_ids) {
        ASSERT_TRUE(job_manager_->wait_for_job(job_id, 30000));
    }
    
    // Verify all completed
    for (const auto& job_id : job_ids) {
        EXPECT_EQ(job_manager_->getJob(job_id)->getStatus(), JobStatus::Completed);
    }
    
    // Verify total records processed
    auto total_records = execute_scalar<int>(
        "SELECT COUNT(DISTINCT source_id) FROM recovery_test_target"
    );
    EXPECT_EQ(total_records, 900); // 300 * 3 tables
}

TEST_F(WorkflowRecoveryTest, TransactionalRecovery) {
    // Test recovery with transactional consistency
    
    populate_test_data(1000);
    
    // Enable strict transactional mode
    ETLJobRequest request;
    request.name = "transactional_job";
    request.source_table = "recovery_test_source";
    request.target_table = "recovery_test_target";
    request.pipeline_config.batch_size = 50;
    request.loader_config["use_transactions"] = true;
    request.loader_config["transaction_size"] = 50;
    
    auto job_id = etl_service_->create_job(request);
    
    // Simulate failure in the middle of a transaction
    std::thread failure_thread([this, job_id]() {
        std::this_thread::sleep_for(2s);
        
        // Force connection failure
        execute_sql("SELECT pg_terminate_backend(pid) FROM pg_stat_activity "
                   "WHERE query LIKE '%recovery_test_target%' AND pid != pg_backend_pid()");
    });
    
    failure_thread.detach();
    
    // Wait for job to fail
    std::this_thread::sleep_for(5s);
    
    // Check that partial transaction was rolled back
    auto partial_count = execute_scalar<int>(
        std::format("SELECT COUNT(*) FROM recovery_test_target WHERE job_id = '{}'", job_id)
    );
    
    // Should be a multiple of transaction_size (50) due to rollback
    EXPECT_EQ(partial_count % 50, 0);
    
    // Recover job
    job_manager_->getJob(job_id)->setStatus(JobStatus::Failed);
    ASSERT_TRUE(job_manager_->retryJob(job_id));
    
    // Wait for completion
    ASSERT_TRUE(job_manager_->wait_for_job(job_id, 30000));
    
    // Verify all records processed with no gaps
    auto final_records = execute_query<int>(
        std::format("SELECT source_id FROM recovery_test_target "
                   "WHERE job_id = '{}' ORDER BY source_id", job_id)
    );
    
    // Check for gaps in sequence
    bool has_gaps = false;
    for (size_t i = 1; i < final_records.size(); ++i) {
        if (final_records[i] != final_records[i-1] + 1) {
            has_gaps = true;
            break;
        }
    }
    
    EXPECT_FALSE(has_gaps);
}

TEST_F(WorkflowRecoveryTest, CheckpointVersioning) {
    // Test checkpoint versioning and compatibility
    
    populate_test_data(500);
    
    // Create job with v1 checkpoint format
    auto job_id = generate_unique_id("versioned_job");
    
    // Write v1 checkpoint
    nlohmann::json v1_checkpoint = {
        {"version", 1},
        {"job_id", job_id},
        {"last_record_id", 100},
        {"state", "processing"}
    };
    
    auto checkpoint_file = checkpoint_dir_ / (job_id + ".checkpoint");
    std::ofstream ofs(checkpoint_file);
    ofs << v1_checkpoint.dump();
    ofs.close();
    
    // Create job config
    ETLJobRequest request;
    request.name = "versioned_checkpoint_job";
    request.source_table = "recovery_test_source";
    request.target_table = "recovery_test_target";
    
    // System should handle v1 checkpoint and upgrade to current version
    auto new_job_id = etl_service_->create_job(request);
    
    // Load and verify checkpoint was upgraded
    std::ifstream ifs(checkpoint_dir_ / (new_job_id + ".checkpoint"));
    nlohmann::json loaded_checkpoint;
    ifs >> loaded_checkpoint;
    
    // Current version should be higher than v1
    EXPECT_GT(loaded_checkpoint["version"].get<int>(), 1);
    
    // Old data should be preserved
    EXPECT_TRUE(loaded_checkpoint.contains("last_record_id"));
    
    // New fields should be added
    EXPECT_TRUE(loaded_checkpoint.contains("checkpoint_timestamp"));
    EXPECT_TRUE(loaded_checkpoint.contains("pipeline_state"));
}

TEST_F(WorkflowRecoveryTest, CascadingFailureRecovery) {
    // Test recovery from cascading failures in dependent jobs
    
    // Create a chain of dependent jobs: A -> B -> C -> D
    std::vector<std::string> job_ids;
    std::vector<std::string> table_names = {
        "cascade_source", "cascade_stage1", "cascade_stage2", "cascade_target"
    };
    
    // Create tables
    for (const auto& table : table_names) {
        execute_sql(std::format(R"(
            CREATE TABLE IF NOT EXISTS {} (
                id SERIAL PRIMARY KEY,
                data VARCHAR(255),
                job_id VARCHAR(100),
                processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )", table));
    }
    
    // Populate source
    for (int i = 0; i < 200; ++i) {
        execute_sql(std::format(
            "INSERT INTO cascade_source (data) VALUES ('data_{}')",
            i
        ));
    }
    
    // Create job chain
    std::string prev_job_id;
    for (size_t i = 0; i < table_names.size() - 1; ++i) {
        ETLJobRequest request;
        request.name = std::format("cascade_job_{}", i);
        request.source_table = table_names[i];
        request.target_table = table_names[i + 1];
        
        std::string job_id;
        if (prev_job_id.empty()) {
            job_id = etl_service_->create_job(request);
        } else {
            auto config = create_job_config(request);
            job_id = scheduler_->submitJob(config, JobPriority::NORMAL, {prev_job_id});
        }
        
        job_ids.push_back(job_id);
        prev_job_id = job_id;
    }
    
    // Simulate failure in job B (index 1)
    std::this_thread::sleep_for(2s);
    job_manager_->getJob(job_ids[1])->setStatus(JobStatus::Failed);
    
    // This should cascade and cancel jobs C and D
    std::this_thread::sleep_for(1s);
    
    // Verify cascade effect
    EXPECT_EQ(job_manager_->getJob(job_ids[0])->getStatus(), JobStatus::Completed);
    EXPECT_EQ(job_manager_->getJob(job_ids[1])->getStatus(), JobStatus::Failed);
    EXPECT_TRUE(job_manager_->getJob(job_ids[2])->getStatus() == JobStatus::Cancelled ||
               job_manager_->getJob(job_ids[2])->getStatus() == JobStatus::Created);
    
    // Recover failed job and dependents
    ASSERT_TRUE(job_manager_->retryJob(job_ids[1]));
    
    // Resume cancelled dependent jobs
    for (size_t i = 2; i < job_ids.size(); ++i) {
        if (job_manager_->getJob(job_ids[i])->getStatus() == JobStatus::Cancelled) {
            job_manager_->resumeJob(job_ids[i]);
        }
    }
    
    // Wait for all to complete
    for (const auto& job_id : job_ids) {
        ASSERT_TRUE(job_manager_->wait_for_job(job_id, 30000));
    }
    
    // Verify full pipeline completed
    for (const auto& job_id : job_ids) {
        EXPECT_EQ(job_manager_->getJob(job_id)->getStatus(), JobStatus::Completed);
    }
    
    // Verify data flowed through all stages
    auto final_count = execute_scalar<int>(
        "SELECT COUNT(*) FROM cascade_target"
    );
    EXPECT_GT(final_count, 0);
}

TEST_F(WorkflowRecoveryTest, ResourceConstrainedRecovery) {
    // Test recovery behavior under resource constraints
    
    // Simulate low memory condition
    job_manager_->setMaxConcurrentJobs(1); // Limit concurrency
    
    // Create multiple jobs that need recovery
    std::vector<std::string> failed_jobs;
    
    for (int i = 0; i < 5; ++i) {
        execute_sql(std::format(R"(
            CREATE TABLE IF NOT EXISTS resource_test_{} (
                id SERIAL PRIMARY KEY,
                data TEXT
            )
        )", i));
        
        // Add large data to simulate memory pressure
        for (int j = 0; j < 100; ++j) {
            execute_sql(std::format(
                "INSERT INTO resource_test_{} (data) VALUES ('{}')",
                i, std::string(1000, 'X') // 1KB per row
            ));
        }
        
        ETLJobRequest request;
        request.name = std::format("resource_constrained_job_{}", i);
        request.source_table = std::format("resource_test_{}", i);
        request.target_table = "recovery_test_target";
        
        auto job_id = etl_service_->create_job(request);
        failed_jobs.push_back(job_id);
        
        // Simulate failure
        simulate_job_failure(job_id, 50);
        job_manager_->getJob(job_id)->setStatus(JobStatus::Failed);
    }
    
    // Attempt recovery of all jobs with resource constraints
    for (const auto& job_id : failed_jobs) {
        ASSERT_TRUE(job_manager_->retryJob(job_id));
    }
    
    // Monitor recovery progress
    auto start_time = std::chrono::steady_clock::now();
    
    // Jobs should be recovered one at a time due to concurrency limit
    int completed_count = 0;
    while (completed_count < failed_jobs.size() && 
           std::chrono::steady_clock::now() - start_time < 120s) {
        
        completed_count = 0;
        int running_count = 0;
        
        for (const auto& job_id : failed_jobs) {
            auto status = job_manager_->getJob(job_id)->getStatus();
            if (status == JobStatus::Completed) {
                completed_count++;
            } else if (status == JobStatus::Running) {
                running_count++;
            }
        }
        
        // Should never have more than 1 job running due to constraint
        EXPECT_LE(running_count, 1);
        
        std::this_thread::sleep_for(1s);
    }
    
    EXPECT_EQ(completed_count, failed_jobs.size());
}

TEST_F(WorkflowRecoveryTest, StatePreservationDuringRecovery) {
    // Test that workflow state is properly preserved during recovery
    
    // Create stateful workflow table
    execute_sql(R"(
        CREATE TABLE IF NOT EXISTS workflow_processing_state (
            job_id VARCHAR(100) PRIMARY KEY,
            current_batch INTEGER DEFAULT 0,
            total_batches INTEGER,
            processing_metadata JSONB,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    )");
    
    populate_test_data(1000);
    
    ETLJobRequest request;
    request.name = "stateful_workflow";
    request.source_table = "recovery_test_source";
    request.target_table = "recovery_test_target";
    request.pipeline_config.batch_size = 100;
    
    auto job_id = etl_service_->create_job(request);
    
    // Initialize workflow state
    execute_sql(std::format(
        "INSERT INTO workflow_processing_state "
        "(job_id, total_batches, processing_metadata) "
        "VALUES ('{}', 10, '{}'::jsonb)",
        job_id,
        R"({
            "validation_rules": ["not_null", "range_check"],
            "transformation_chain": ["normalize", "map_concepts"],
            "custom_params": {"threshold": 0.95}
        })"
    ));
    
    // Process some batches and update state
    for (int batch = 1; batch <= 3; ++batch) {
        execute_sql(std::format(
            "UPDATE workflow_processing_state "
            "SET current_batch = {}, last_updated = CURRENT_TIMESTAMP "
            "WHERE job_id = '{}'",
            batch, job_id
        ));
    }
    
    // Simulate failure
    simulate_job_failure(job_id, 300);
    job_manager_->getJob(job_id)->setStatus(JobStatus::Failed);
    
    // Capture state before recovery
    auto state_before = execute_query<std::tuple<int, std::string>>(
        std::format("SELECT current_batch, processing_metadata::text "
                   "FROM workflow_processing_state WHERE job_id = '{}'", job_id)
    );
    
    ASSERT_FALSE(state_before.empty());
    auto [batch_before, metadata_before] = state_before[0];
    
    // Recover job
    ASSERT_TRUE(job_manager_->retryJob(job_id));
    ASSERT_TRUE(job_manager_->wait_for_job(job_id, 30000));
    
    // Verify state was preserved and updated
    auto state_after = execute_query<std::tuple<int, std::string>>(
        std::format("SELECT current_batch, processing_metadata::text "
                   "FROM workflow_processing_state WHERE job_id = '{}'", job_id)
    );
    
    ASSERT_FALSE(state_after.empty());
    auto [batch_after, metadata_after] = state_after[0];
    
    // Batch should have progressed
    EXPECT_GT(batch_after, batch_before);
    EXPECT_EQ(batch_after, 10); // All batches processed
    
    // Metadata should be preserved
    EXPECT_EQ(metadata_after, metadata_before);
}

} // namespace omop::test::integration::workflow