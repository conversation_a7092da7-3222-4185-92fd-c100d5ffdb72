test_case,person_id,field_name,field_value,expected_behavior,notes
NULL_VALUES,2001,birth_date,,Should use default or reject,Testing NULL handling
EMPTY_STRING,2002,gender,"",Should validate and reject,Empty string validation
SPECIAL_CHARS,2003,name,"<PERSON><PERSON><PERSON>, Jr.",Should escape properly,Special character handling
UTF8_CHARS,2004,name,<PERSON>,Should handle UTF-8,Unicode support
VERY_LONG_STRING,2005,notes,Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua Ut enim ad minim veniam quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat,Should truncate or handle,Length limits
NEGATIVE_ID,2006,person_id,-1,Should reject invalid ID,ID validation
ZERO_ID,2007,person_id,0,Should reject zero ID,ID validation
FLOAT_ID,2008,person_id,123.45,Should convert or reject,Type conversion
FUTURE_BIRTH,2009,birth_date,2030-01-01,Should reject future date,Date validation
ANCIENT_DATE,2010,birth_date,1800-01-01,Should validate age limit,Historical date
INVALID_DATE_FORMAT,2011,visit_date,01-15-2024,Should parse or reject,Date format handling
DECIMAL_PRECISION,2012,weight,123.456789012345,Should round appropriately,Precision handling
SCIENTIFIC_NOTATION,2013,lab_value,1.23E+10,Should parse correctly,Number format
NEGATIVE_QUANTITY,2014,drug_quantity,-10,Should reject negative,Business rule
DUPLICATE_KEY,2015,visit_id,V001,Should handle duplicate,Uniqueness constraint
MISSING_REQUIRED,2016,,,Should reject record,Required field validation
INVALID_CONCEPT,2017,gender_concept_id,99999999,Should map to 0,Invalid concept mapping
CASE_SENSITIVITY,2018,gender,MALE,Should normalize case,Case handling
WHITESPACE,2019,diagnosis_code,"  I10  ",Should trim whitespace,Whitespace handling
INJECTION_ATTEMPT,2020,notes,"'; DROP TABLE person; --",Should escape safely,SQL injection prevention