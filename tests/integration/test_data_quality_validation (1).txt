// Integration test for data quality validation across the ETL pipeline
#include <gtest/gtest.h>
#include "common/validation.h"
#include "transform/vocabulary_service.h"
#include "core/interfaces.h"
#include "test_helpers/database_fixture.h"
#include <random>
#include <regex>

namespace omop::test::integration {

/**
 * @brief Data quality and validation integration test
 * 
 * Tests comprehensive data quality checks and validation rules
 * throughout the ETL pipeline.
 */
class DataQualityValidationTest : public DatabaseFixture {
protected:
    void SetUp() override {
        DatabaseFixture::SetUp();
        setupValidationRules();
        createTestDataWithQualityIssues();
    }

    void setupValidationRules() {
        // Initialize validation engine
        validation_engine_ = std::make_unique<common::ValidationEngine>();
        
        // Register custom validators
        registerDateValidators();
        registerNumericValidators();
        registerCodeValidators();
        registerBusinessRuleValidators();
    }

    void registerDateValidators() {
        // Date range validator
        validation_engine_->register_validator("date_range",
            [](const std::any& value, const std::unordered_map<std::string, std::any>& params) {
                if (value.type() != typeid(std::string)) return false;
                
                auto date_str = std::any_cast<std::string>(value);
                auto min_date = std::any_cast<std::string>(params.at("min"));
                auto max_date = std::any_cast<std::string>(params.at("max"));
                
                return date_str >= min_date && date_str <= max_date;
            });

        // Future date validator
        validation_engine_->register_validator("not_future",
            [](const std::any& value, const std::unordered_map<std::string, std::any>& params) {
                if (value.type() != typeid(std::string)) return false;
                
                auto date_str = std::any_cast<std::string>(value);
                auto now = std::chrono::system_clock::now();
                auto now_time_t = std::chrono::system_clock::to_time_t(now);
                auto now_str = std::string(std::ctime(&now_time_t));
                
                return date_str <= now_str.substr(0, 10);
            });
    }

    void registerNumericValidators() {
        // Numeric range validator
        validation_engine_->register_validator("numeric_range",
            [](const std::any& value, const std::unordered_map<std::string, std::any>& params) {
                try {
                    double val = 0.0;
                    if (value.type() == typeid(int)) {
                        val = static_cast<double>(std::any_cast<int>(value));
                    } else if (value.type() == typeid(double)) {
                        val = std::any_cast<double>(value);
                    } else {
                        return false;
                    }
                    
                    double min_val = std::any_cast<double>(params.at("min"));
                    double max_val = std::any_cast<double>(params.at("max"));
                    
                    return val >= min_val && val <= max_val;
                } catch (...) {
                    return false;
                }
            });

        // Precision validator
        validation_engine_->register_validator("decimal_precision",
            [](const std::any& value, const std::unordered_map<std::string, std::any>& params) {
                if (value.type() != typeid(double)) return false;
                
                auto val = std::any_cast<double>(value);
                auto precision = std::any_cast<int>(params.at("precision"));
                
                std::stringstream ss;
                ss << std::fixed << std::setprecision(precision) << val;
                double rounded;
                ss >> rounded;
                
                return std::abs(val - rounded) < std::numeric_limits<double>::epsilon();
            });
    }

    void registerCodeValidators() {
        // ICD code format validator
        validation_engine_->register_validator("icd_format",
            [](const std::any& value, const std::unordered_map<std::string, std::any>& params) {
                if (value.type() != typeid(std::string)) return false;
                
                auto code = std::any_cast<std::string>(value);
                auto version = std::any_cast<std::string>(params.at("version"));
                
                if (version == "ICD10") {
                    // Basic ICD-10 format: letter followed by numbers and optional decimal
                    std::regex icd10_pattern(R"([A-Z]\d{2}(\.\d{1,2})?)");
                    return std::regex_match(code, icd10_pattern);
                } else if (version == "ICD9") {
                    // Basic ICD-9 format: numbers with optional decimal
                    std::regex icd9_pattern(R"(\d{3}(\.\d{1,2})?)");
                    return std::regex_match(code, icd9_pattern);
                }
                
                return false;
            });

        // CPT code validator
        validation_engine_->register_validator("cpt_format",
            [](const std::any& value, const std::unordered_map<std::string, std::any>& params) {
                if (value.type() != typeid(std::string)) return false;
                
                auto code = std::any_cast<std::string>(value);
                // CPT codes are 5 digits
                std::regex cpt_pattern(R"(\d{5})");
                return std::regex_match(code, cpt_pattern);
            });
    }

    void registerBusinessRuleValidators() {
        // Age consistency validator
        validation_engine_->register_validator("age_consistency",
            [](const std::any& value, const std::unordered_map<std::string, std::any>& params) {
                auto record = std::any_cast<core::Record>(value);
                
                try {
                    auto birth_date = record.getFieldAs<std::string>("birth_date");
                    auto death_date = record.getFieldOptional("death_date");
                    
                    if (death_date.has_value() && death_date->type() == typeid(std::string)) {
                        auto death_str = std::any_cast<std::string>(*death_date);
                        
                        // Death date must be after birth date
                        if (death_str < birth_date) {
                            return false;
                        }
                        
                        // Calculate age at death
                        int birth_year = std::stoi(birth_date.substr(0, 4));
                        int death_year = std::stoi(death_str.substr(0, 4));
                        int age = death_year - birth_year;
                        
                        // Age should be reasonable (0-150)
                        return age >= 0 && age <= 150;
                    }
                    
                    return true;
                } catch (...) {
                    return false;
                }
            });

        // Drug dosage validator
        validation_engine_->register_validator("drug_dosage",
            [](const std::any& value, const std::unordered_map<std::string, std::any>& params) {
                auto record = std::any_cast<core::Record>(value);
                
                try {
                    auto drug_name = record.getFieldAs<std::string>("drug_name");
                    auto dosage = record.getFieldAs<double>("dosage");
                    auto unit = record.getFieldAs<std::string>("unit");
                    
                    // Check common drug dosage ranges
                    if (drug_name == "Metformin") {
                        if (unit == "mg") {
                            return dosage >= 500 && dosage <= 2000;
                        }
                    } else if (drug_name == "Lisinopril") {
                        if (unit == "mg") {
                            return dosage >= 2.5 && dosage <= 40;
                        }
                    }
                    
                    // Default range for unknown drugs
                    return dosage > 0 && dosage < 10000;
                } catch (...) {
                    return false;
                }
            });
    }

    void createTestDataWithQualityIssues() {
        test_data_dir_ = std::filesystem::temp_directory_path() / "quality_test_data";
        std::filesystem::create_directories(test_data_dir_);
        
        createDataWithDateIssues();
        createDataWithNumericIssues();
        createDataWithCodeIssues();
        createDataWithConsistencyIssues();
    }

    void createDataWithDateIssues() {
        std::ofstream file(test_data_dir_ / "date_issues.csv");
        file << "patient_id,event_date,birth_date,death_date\n";
        file << "1,2024-01-15,1950-01-01,\n"; // Valid
        file << "2,2025-12-31,1960-01-01,\n"; // Future date
        file << "3,2024-01-15,2025-01-01,\n"; // Future birth date
        file << "4,2024-01-15,1980-01-01,1970-01-01\n"; // Death before birth
        file << "5,invalid-date,1990-01-01,\n"; // Invalid date format
    }

    void createDataWithNumericIssues() {
        std::ofstream file(test_data_dir_ / "numeric_issues.csv");
        file << "patient_id,weight_kg,height_cm,bmi,temperature_f\n";
        file << "1,70.5,175,23.0,98.6\n"; // Valid
        file << "2,-10,180,25.0,98.6\n"; // Negative weight
        file << "3,500,170,173.0,98.6\n"; // Unrealistic weight
        file << "4,80,50,320.0,98.6\n"; // Unrealistic height
        file << "5,75,180,23.148148,150.0\n"; // Too many decimals, high temp
    }

    void createDataWithCodeIssues() {
        std::ofstream file(test_data_dir_ / "code_issues.csv");
        file << "patient_id,diagnosis_code,procedure_code,drug_code\n";
        file << "1,I10,99213,1234567\n"; // Valid
        file << "2,ABC123,99213,1234567\n"; // Invalid ICD format
        file << "3,I10,123,1234567\n"; // Invalid CPT (too short)
        file << "4,I10,ABCDE,1234567\n"; // Invalid CPT (letters)
        file << "5,999.99,999999,ABC\n"; // ICD9 format, CPT too long
    }

    void createDataWithConsistencyIssues() {
        std::ofstream file(test_data_dir_ / "consistency_issues.csv");
        file << "patient_id,drug_name,dosage,unit,start_date,end_date\n";
        file << "1,Metformin,1000,mg,2024-01-01,2024-12-31\n"; // Valid
        file << "2,Metformin,5000,mg,2024-01-01,2024-12-31\n"; // Too high dose
        file << "3,Lisinopril,100,mg,2024-01-01,2024-12-31\n"; // Too high dose
        file << "4,Aspirin,81,mg,2024-12-31,2024-01-01\n"; // End before start
        file << "5,Unknown Drug,0,mg,2024-01-01,2024-12-31\n"; // Zero dose
    }

protected:
    std::filesystem::path test_data_dir_;
    std::unique_ptr<common::ValidationEngine> validation_engine_;
};

// Test date validation rules
TEST_F(DataQualityValidationTest, TestDateValidation) {
    // Load test data with date issues
    auto file_path = test_data_dir_ / "date_issues.csv";
    extract::CsvExtractor extractor;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = file_path.string();
    
    core::ProcessingContext context;
    extractor.initialize(config, context);
    
    // Extract and validate records
    auto batch = extractor.extract_batch(10, context);
    
    int valid_count = 0;
    int invalid_count = 0;
    
    for (const auto& record : batch) {
        // Validate future dates
        auto event_date = record.getFieldOptional("event_date");
        if (event_date.has_value()) {
            std::unordered_map<std::string, std::any> params;
            bool is_valid = validation_engine_->validate("not_future", *event_date, params);
            
            if (is_valid) {
                valid_count++;
            } else {
                invalid_count++;
                context.log("warning", "Future date detected");
            }
        }
        
        // Validate age consistency
        std::unordered_map<std::string, std::any> params;
        bool age_valid = validation_engine_->validate("age_consistency", record, params);
        
        if (!age_valid) {
            context.log("error", "Age consistency validation failed");
        }
    }
    
    EXPECT_GT(valid_count, 0);
    EXPECT_GT(invalid_count, 0); // We expect some invalid dates in test data
}

// Test numeric validation rules
TEST_F(DataQualityValidationTest, TestNumericValidation) {
    auto file_path = test_data_dir_ / "numeric_issues.csv";
    extract::CsvExtractor extractor;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = file_path.string();
    
    core::ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    
    for (const auto& record : batch) {
        // Validate weight
        auto weight = record.getFieldOptional("weight_kg");
        if (weight.has_value() && weight->type() == typeid(double)) {
            std::unordered_map<std::string, std::any> params;
            params["min"] = 1.0;
            params["max"] = 300.0;
            
            bool valid = validation_engine_->validate("numeric_range", *weight, params);
            
            if (!valid) {
                auto weight_val = std::any_cast<double>(*weight);
                context.log("warning", 
                    std::format("Invalid weight: {} kg", weight_val));
            }
        }
        
        // Validate height
        auto height = record.getFieldOptional("height_cm");
        if (height.has_value() && height->type() == typeid(double)) {
            std::unordered_map<std::string, std::any> params;
            params["min"] = 50.0;
            params["max"] = 250.0;
            
            bool valid = validation_engine_->validate("numeric_range", *height, params);
            
            if (!valid) {
                auto height_val = std::any_cast<double>(*height);
                context.log("warning", 
                    std::format("Invalid height: {} cm", height_val));
            }
        }
        
        // Validate BMI calculation
        if (weight.has_value() && height.has_value()) {
            auto weight_val = std::any_cast<double>(*weight);
            auto height_val = std::any_cast<double>(*height);
            auto bmi = record.getFieldOptional("bmi");
            
            if (bmi.has_value() && bmi->type() == typeid(double)) {
                auto bmi_val = std::any_cast<double>(*bmi);
                double calculated_bmi = weight_val / ((height_val / 100) * (height_val / 100));
                
                if (std::abs(bmi_val - calculated_bmi) > 0.1) {
                    context.log("error", 
                        std::format("BMI calculation mismatch: {} vs {}", 
                                  bmi_val, calculated_bmi));
                }
            }
        }
    }
}

// Test medical code validation
TEST_F(DataQualityValidationTest, TestMedicalCodeValidation) {
    auto file_path = test_data_dir_ / "code_issues.csv";
    extract::CsvExtractor extractor;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = file_path.string();
    
    core::ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    
    int valid_icd_count = 0;
    int invalid_icd_count = 0;
    int valid_cpt_count = 0;
    int invalid_cpt_count = 0;
    
    for (const auto& record : batch) {
        // Validate ICD codes
        auto diag_code = record.getFieldOptional("diagnosis_code");
        if (diag_code.has_value() && diag_code->type() == typeid(std::string)) {
            std::unordered_map<std::string, std::any> params;
            params["version"] = std::string("ICD10");
            
            bool valid_icd10 = validation_engine_->validate("icd_format", *diag_code, params);
            
            if (!valid_icd10) {
                // Try ICD9
                params["version"] = std::string("ICD9");
                bool valid_icd9 = validation_engine_->validate("icd_format", *diag_code, params);
                
                if (valid_icd9) {
                    context.log("info", "ICD-9 code detected, conversion needed");
                    valid_icd_count++;
                } else {
                    invalid_icd_count++;
                    context.log("error", 
                        std::format("Invalid diagnosis code: {}", 
                                  std::any_cast<std::string>(*diag_code)));
                }
            } else {
                valid_icd_count++;
            }
        }
        
        // Validate CPT codes
        auto proc_code = record.getFieldOptional("procedure_code");
        if (proc_code.has_value() && proc_code->type() == typeid(std::string)) {
            std::unordered_map<std::string, std::any> params;
            
            bool valid = validation_engine_->validate("cpt_format", *proc_code, params);
            
            if (valid) {
                valid_cpt_count++;
            } else {
                invalid_cpt_count++;
                context.log("error", 
                    std::format("Invalid CPT code: {}", 
                              std::any_cast<std::string>(*proc_code)));
            }
        }
    }
    
    EXPECT_GT(valid_icd_count, 0);
    EXPECT_GT(invalid_icd_count, 0); // Expected due to test data
    EXPECT_GT(valid_cpt_count, 0);
    EXPECT_GT(invalid_cpt_count, 0); // Expected due to test data
}

// Test business rule validation
TEST_F(DataQualityValidationTest, TestBusinessRuleValidation) {
    auto file_path = test_data_dir_ / "consistency_issues.csv";
    extract::CsvExtractor extractor;
    
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = file_path.string();
    
    core::ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    
    for (const auto& record : batch) {
        // Validate drug dosages
        std::unordered_map<std::string, std::any> params;
        bool dosage_valid = validation_engine_->validate("drug_dosage", record, params);
        
        if (!dosage_valid) {
            auto drug_name = record.getFieldAs<std::string>("drug_name");
            auto dosage = record.getFieldAs<double>("dosage");
            
            context.log("warning", 
                std::format("Invalid dosage for {}: {} mg", drug_name, dosage));
        }
        
        // Validate date ranges
        auto start_date = record.getFieldOptional("start_date");
        auto end_date = record.getFieldOptional("end_date");
        
        if (start_date.has_value() && end_date.has_value()) {
            auto start_str = std::any_cast<std::string>(*start_date);
            auto end_str = std::any_cast<std::string>(*end_date);
            
            if (end_str < start_str) {
                context.log("error", "End date before start date");
            }
        }
    }
}

// Test validation pipeline integration
TEST_F(DataQualityValidationTest, TestValidationPipelineIntegration) {
    // Create a complete validation pipeline
    core::PipelineConfig pipeline_config;
    pipeline_config.batch_size = 100;
    pipeline_config.validate_records = true;
    pipeline_config.error_threshold = 0.2; // Allow 20% errors
    
    auto pipeline = std::make_unique<core::ETLPipeline>(pipeline_config);
    
    // Add pre-processor for validation
    pipeline->add_pre_processor(
        [this](core::RecordBatch& batch, core::ProcessingContext& context) {
            auto it = batch.begin();
            while (it != batch.end()) {
                bool valid = true;
                
                // Run all validations
                std::vector<std::string> errors;
                
                // Date validations
                auto event_date = it->getFieldOptional("event_date");
                if (event_date.has_value()) {
                    std::unordered_map<std::string, std::any> params;
                    if (!validation_engine_->validate("not_future", *event_date, params)) {
                        errors.push_back("Future date detected");
                        valid = false;
                    }
                }
                
                // Numeric validations
                auto weight = it->getFieldOptional("weight_kg");
                if (weight.has_value()) {
                    std::unordered_map<std::string, std::any> params;
                    params["min"] = 1.0;
                    params["max"] = 300.0;
                    
                    if (!validation_engine_->validate("numeric_range", *weight, params)) {
                        errors.push_back("Weight out of range");
                        valid = false;
                    }
                }
                
                if (!valid) {
                    // Log validation errors
                    for (const auto& error : errors) {
                        context.log("validation_error", error);
                    }
                    
                    // Remove invalid record
                    it = batch.getRecords().erase(it);
                    context.increment_errors();
                } else {
                    ++it;
                }
            }
        });
    
    // Set up extractor for all test files
    auto extractor = std::make_unique<extract::MultiFileCsvExtractor>();
    std::unordered_map<std::string, std::any> extract_config;
    extract_config["files"] = std::vector<std::string>{
        (test_data_dir_ / "date_issues.csv").string(),
        (test_data_dir_ / "numeric_issues.csv").string(),
        (test_data_dir_ / "code_issues.csv").string(),
        (test_data_dir_ / "consistency_issues.csv").string()
    };
    
    core::ProcessingContext context;
    extractor->initialize(extract_config, context);
    pipeline->set_extractor(std::move(extractor));
    
    // Set up transformer
    pipeline->set_transformer(std::make_unique<transform::TransformationEngine>());
    
    // Set up loader (mock for testing)
    class MockLoader : public core::ILoader {
    public:
        void initialize(const std::unordered_map<std::string, std::any>& config,
                       core::ProcessingContext& context) override {}
        
        bool load(const core::Record& record, core::ProcessingContext& context) override {
            loaded_records_.push_back(record);
            return true;
        }
        
        size_t load_batch(const core::RecordBatch& batch,
                         core::ProcessingContext& context) override {
            for (const auto& record : batch) {
                loaded_records_.push_back(record);
            }
            return batch.size();
        }
        
        void commit(core::ProcessingContext& context) override {}
        void rollback(core::ProcessingContext& context) override {}
        std::string get_type() const override { return "mock"; }
        void finalize(core::ProcessingContext& context) override {}
        
        std::unordered_map<std::string, std::any> get_statistics() const override {
            return {{"loaded_count", loaded_records_.size()}};
        }
        
        std::vector<core::Record> loaded_records_;
    };
    
    auto mock_loader = std::make_unique<MockLoader>();
    auto loader_ptr = mock_loader.get();
    pipeline->set_loader(std::move(mock_loader));
    
    // Run pipeline
    auto job = pipeline->start("validation_test");
    auto result = job.get();
    
    // Verify results
    EXPECT_EQ(result.status, core::JobStatus::Completed);
    EXPECT_GT(result.total_records, 0);
    EXPECT_GT(result.error_records, 0); // We expect validation errors
    EXPECT_LT(result.error_records / static_cast<double>(result.total_records), 0.5); // Less than 50% errors
    
    // Check that only valid records were loaded
    EXPECT_GT(loader_ptr->loaded_records_.size(), 0);
    EXPECT_LT(loader_ptr->loaded_records_.size(), result.total_records);
}

// Test validation performance with large datasets
TEST_F(DataQualityValidationTest, TestValidationPerformance) {
    // Create large test dataset
    auto large_file = test_data_dir_ / "large_validation_test.csv";
    std::ofstream file(large_file);
    file << "patient_id,value,date,code\n";
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> value_dist(-100, 1000);
    std::uniform_int_distribution<> year_dist(2020, 2025);
    std::uniform_int_distribution<> code_dist(10000, 99999);
    
    const size_t num_records = 10000;
    for (size_t i = 0; i < num_records; ++i) {
        file << i << ","
             << value_dist(gen) << ","
             << year_dist(gen) << "-01-01,"
             << code_dist(gen) << "\n";
    }
    file.close();
    
    // Measure validation performance
    auto start_time = std::chrono::high_resolution_clock::now();
    
    extract::CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = large_file.string();
    
    core::ProcessingContext context;
    extractor.initialize(config, context);
    
    size_t total_validated = 0;
    size_t batch_size = 1000;
    
    while (extractor.has_more_data()) {
        auto batch = extractor.extract_batch(batch_size, context);
        
        for (const auto& record : batch) {
            // Validate numeric range
            auto value = record.getFieldOptional("value");
            if (value.has_value()) {
                std::unordered_map<std::string, std::any> params;
                params["min"] = 0.0;
                params["max"] = 500.0;
                
                validation_engine_->validate("numeric_range", *value, params);
            }
            
            // Validate date
            auto date = record.getFieldOptional("date");
            if (date.has_value()) {
                std::unordered_map<std::string, std::any> params;
                validation_engine_->validate("not_future", *date, params);
            }
            
            // Validate code format
            auto code = record.getFieldOptional("code");
            if (code.has_value()) {
                std::unordered_map<std::string, std::any> params;
                validation_engine_->validate("cpt_format", *code, params);
            }
            
            total_validated++;
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    double records_per_second = (total_validated * 1000.0) / duration.count();
    
    EXPECT_EQ(total_validated, num_records);
    EXPECT_GT(records_per_second, 1000); // Should validate at least 1000 records/second
    
    // Log performance metrics
    context.log("info", 
        std::format("Validated {} records in {} ms ({:.2f} records/second)",
                  total_validated, duration.count(), records_per_second));
}

} // namespace omop::test::integration