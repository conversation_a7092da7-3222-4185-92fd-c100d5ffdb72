/**
 * @file csv_extractor_test.cpp
 * @brief Unit tests for CSV extractor functionality
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/csv_extractor.h"
#include "common/exceptions.h"
#include "core/interfaces.h"
#include <filesystem>
#include <fstream>
#include <chrono>

using namespace omop::extract;
using namespace omop::core;
using namespace omop::common;
using ::testing::_;
using ::testing::Return;
using ::testing::Throw;

// Test fixture for CSV extractor tests
class CsvExtractorTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for test files
        test_dir_ = std::filesystem::temp_directory_path() / "omop_csv_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        // Clean up test files
        std::filesystem::remove_all(test_dir_);
    }

    // Helper function to create test CSV file
    std::string createTestCsv(const std::string& filename, const std::string& content) {
        std::filesystem::path filepath = test_dir_ / filename;
        std::ofstream file(filepath);
        file << content;
        file.close();
        return filepath.string();
    }

    std::filesystem::path test_dir_;
};

// Test CSV field parser functionality
class CsvFieldParserTest : public ::testing::Test {
protected:
    CsvOptions options_;
    std::unique_ptr<CsvFieldParser> parser_;

    void SetUp() override {
        options_.delimiter = ',';
        options_.quote_char = '"';
        options_.escape_char = '\\';
        options_.trim_fields = true;
        parser_ = std::make_unique<CsvFieldParser>(options_);
    }
};

// Tests basic field parsing without quotes
TEST_F(CsvFieldParserTest, ParseSimpleField) {
    std::string input = "field1,field2,field3";
    size_t pos = 0;
    
    std::string field1 = parser_->parse_field(input, pos);
    EXPECT_EQ("field1", field1);
    EXPECT_EQ(7, pos);
    
    std::string field2 = parser_->parse_field(input, pos);
    EXPECT_EQ("field2", field2);
    EXPECT_EQ(14, pos);
    
    std::string field3 = parser_->parse_field(input, pos);
    EXPECT_EQ("field3", field3);
}

// Tests field parsing with quotes
TEST_F(CsvFieldParserTest, ParseQuotedField) {
    std::string input = "\"field with, comma\",\"field with \"\" quotes\"";
    size_t pos = 0;
    
    std::string field1 = parser_->parse_field(input, pos);
    EXPECT_EQ("field with, comma", field1);
    
    std::string field2 = parser_->parse_field(input, pos);
    EXPECT_EQ("field with \"\" quotes", field2);
}

// Tests field parsing with escape characters
TEST_F(CsvFieldParserTest, ParseEscapedField) {
    std::string input = "field\\,with\\,escapes,normal field";
    size_t pos = 0;
    
    std::string field1 = parser_->parse_field(input, pos);
    EXPECT_EQ("field,with,escapes", field1);
}

// Tests parsing complete line into fields
TEST_F(CsvFieldParserTest, ParseCompleteLine) {
    std::string line = "John,Doe,30,\"New York, NY\"";
    
    auto fields = parser_->parse_line(line);
    ASSERT_EQ(4, fields.size());
    EXPECT_EQ("John", fields[0]);
    EXPECT_EQ("Doe", fields[1]);
    EXPECT_EQ("30", fields[2]);
    EXPECT_EQ("New York, NY", fields[3]);
}

// Tests field type conversion
TEST_F(CsvFieldParserTest, ConvertFieldTypes) {
    // Test integer conversion
    auto int_value = parser_->convert_field("42", "integer");
    ASSERT_TRUE(int_value.has_value());
    EXPECT_EQ(42LL, std::any_cast<long long>(int_value));
    
    // Test double conversion
    auto double_value = parser_->convert_field("3.14", "double");
    ASSERT_TRUE(double_value.has_value());
    EXPECT_DOUBLE_EQ(3.14, std::any_cast<double>(double_value));
    
    // Test boolean conversion
    auto bool_value = parser_->convert_field("TRUE", "boolean");
    ASSERT_TRUE(bool_value.has_value());
    EXPECT_TRUE(std::any_cast<bool>(bool_value));
    
    // Test null value handling
    auto null_value = parser_->convert_field("", "");
    EXPECT_FALSE(null_value.has_value());
}

// Tests date parsing
TEST_F(CsvFieldParserTest, ParseDateField) {
    options_.date_format = "%Y-%m-%d";
    parser_ = std::make_unique<CsvFieldParser>(options_);
    
    auto date_value = parser_->convert_field("2024-01-15", "date");
    ASSERT_TRUE(date_value.has_value());
    EXPECT_TRUE(date_value.type() == typeid(std::chrono::system_clock::time_point));
}

// Tests automatic type inference
TEST_F(CsvFieldParserTest, InferFieldType) {
    // Integer inference
    auto int_val = parser_->convert_field("12345", "");
    ASSERT_TRUE(int_val.has_value());
    EXPECT_TRUE(int_val.type() == typeid(long long));
    
    // Double inference
    auto double_val = parser_->convert_field("123.45", "");
    ASSERT_TRUE(double_val.has_value());
    EXPECT_TRUE(double_val.type() == typeid(double));
    
    // Boolean inference
    auto bool_val = parser_->convert_field("true", "");
    ASSERT_TRUE(bool_val.has_value());
    EXPECT_TRUE(bool_val.type() == typeid(bool));
    
    // String fallback
    auto string_val = parser_->convert_field("not a number", "");
    ASSERT_TRUE(string_val.has_value());
    EXPECT_TRUE(string_val.type() == typeid(std::string));
}

// Tests basic CSV extraction
TEST_F(CsvExtractorTest, ExtractSimpleCsv) {
    std::string csv_content = 
        "name,age,city\n"
        "John,30,New York\n"
        "Jane,25,Los Angeles\n";
    
    std::string filepath = createTestCsv("simple.csv", csv_content);
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    
    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
    
    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());
    
    auto records = batch.getRecords();
    EXPECT_EQ("John", std::any_cast<std::string>(*records[0].get_field("name")));
    EXPECT_EQ("30", std::any_cast<std::string>(*records[0].get_field("age")));
    EXPECT_EQ("Jane", std::any_cast<std::string>(*records[1].get_field("name")));
}

// Tests CSV extraction with custom delimiter
TEST_F(CsvExtractorTest, ExtractWithCustomDelimiter) {
    std::string csv_content = 
        "name|age|city\n"
        "John|30|New York\n"
        "Jane|25|Los Angeles\n";
    
    std::string filepath = createTestCsv("pipe_delimited.csv", csv_content);
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["delimiter"] = '|';
    
    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
    
    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());
}

// Tests CSV extraction without header
TEST_F(CsvExtractorTest, ExtractWithoutHeader) {
    std::string csv_content = 
        "John,30,New York\n"
        "Jane,25,Los Angeles\n";
    
    std::string filepath = createTestCsv("no_header.csv", csv_content);
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["has_header"] = false;
    config["column_names"] = std::vector<std::string>{"name", "age", "city"};
    
    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
    
    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(2, batch.size());
    
    auto records = batch.getRecords();
    EXPECT_EQ("John", std::any_cast<std::string>(*records[0].get_field("name")));
}

// Tests CSV extraction with specified column types
TEST_F(CsvExtractorTest, ExtractWithColumnTypes) {
    std::string csv_content = 
        "name,age,salary,active\n"
        "John,30,50000.50,true\n"
        "Jane,25,45000.00,false\n";
    
    std::string filepath = createTestCsv("typed.csv", csv_content);
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["column_types"] = std::vector<std::string>{"string", "integer", "double", "boolean"};
    
    ProcessingContext context;
    ASSERT_NO_THROW(extractor.initialize(config, context));
    
    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();
    
    EXPECT_EQ(30LL, std::any_cast<long long>(*records[0].get_field("age")));
    EXPECT_DOUBLE_EQ(50000.50, std::any_cast<double>(*records[0].get_field("salary")));
    EXPECT_TRUE(std::any_cast<bool>(*records[0].get_field("active")));
}

// Tests batch extraction
TEST_F(CsvExtractorTest, ExtractInBatches) {
    std::string csv_content = "id,value\n";
    for (int i = 1; i <= 25; ++i) {
        csv_content += std::to_string(i) + "," + std::to_string(i * 10) + "\n";
    }
    
    std::string filepath = createTestCsv("batch_test.csv", csv_content);
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    // Extract first batch
    auto batch1 = extractor.extract_batch(10, context);
    EXPECT_EQ(10, batch1.size());
    
    // Extract second batch
    auto batch2 = extractor.extract_batch(10, context);
    EXPECT_EQ(10, batch2.size());
    
    // Extract remaining
    auto batch3 = extractor.extract_batch(10, context);
    EXPECT_EQ(5, batch3.size());
    
    // No more data
    EXPECT_FALSE(extractor.has_more_data());
}

// Tests error handling for missing file
TEST_F(CsvExtractorTest, HandleMissingFile) {
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = "/nonexistent/file.csv";
    
    ProcessingContext context;
    EXPECT_THROW(extractor.initialize(config, context), ExtractionException);
}

// Tests skip lines functionality
TEST_F(CsvExtractorTest, SkipLines) {
    std::string csv_content = 
        "# Comment line 1\n"
        "# Comment line 2\n"
        "name,age,city\n"
        "John,30,New York\n";
    
    std::string filepath = createTestCsv("skip_lines.csv", csv_content);
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["skip_lines"] = size_t(2);
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    ASSERT_EQ(1, batch.size());
    
    auto records = batch.getRecords();
    EXPECT_EQ("John", std::any_cast<std::string>(*records[0].get_field("name")));
}

// Tests max lines functionality
TEST_F(CsvExtractorTest, MaxLines) {
    std::string csv_content = "id\n";
    for (int i = 1; i <= 100; ++i) {
        csv_content += std::to_string(i) + "\n";
    }
    
    std::string filepath = createTestCsv("max_lines.csv", csv_content);
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["max_lines"] = std::optional<size_t>(10);
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(100, context);
    EXPECT_EQ(10, batch.size());
    EXPECT_FALSE(extractor.has_more_data());
}

// Tests column type inference
TEST_F(CsvExtractorTest, InferColumnTypes) {
    std::string csv_content = 
        "int_col,float_col,bool_col,date_col,string_col\n"
        "123,45.67,true,2024-01-15,text\n"
        "456,78.90,false,2024-02-20,more text\n"
        "789,12.34,1,2024-03-25,another text\n";
    
    std::string filepath = createTestCsv("infer_types.csv", csv_content);
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    // Don't specify column types - let it infer
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    auto batch = extractor.extract_batch(10, context);
    auto records = batch.getRecords();
    
    // Check inferred types
    auto int_val = records[0].get_field("int_col");
    EXPECT_TRUE(int_val->type() == typeid(long long));
    
    auto float_val = records[0].get_field("float_col");
    EXPECT_TRUE(float_val->type() == typeid(double));
    
    auto bool_val = records[0].get_field("bool_col");
    EXPECT_TRUE(bool_val->type() == typeid(bool));
}

// Tests statistics collection
TEST_F(CsvExtractorTest, GetStatistics) {
    std::string csv_content = 
        "id,value\n"
        "1,100\n"
        "2,200\n";
    
    std::string filepath = createTestCsv("stats.csv", csv_content);
    
    CsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    
    ProcessingContext context;
    extractor.initialize(config, context);
    extractor.extract_batch(10, context);
    extractor.finalize(context);
    
    auto stats = extractor.get_statistics();
    EXPECT_EQ(filepath, std::any_cast<std::string>(stats["filepath"]));
    EXPECT_EQ(2, std::any_cast<size_t>(stats["extracted_count"]));
    EXPECT_EQ(2, std::any_cast<size_t>(stats["column_count"]));
}

// Tests multi-file CSV extractor
TEST_F(CsvExtractorTest, MultiFileCsvExtractor) {
    // Create multiple CSV files
    std::string csv1 = "id,value\n1,100\n2,200\n";
    std::string csv2 = "id,value\n3,300\n4,400\n";
    std::string csv3 = "id,value\n5,500\n6,600\n";
    
    std::vector<std::string> files = {
        createTestCsv("multi1.csv", csv1),
        createTestCsv("multi2.csv", csv2),
        createTestCsv("multi3.csv", csv3)
    };
    
    MultiFileCsvExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["files"] = files;
    config["skip_headers_after_first"] = true;
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    // Extract all records
    std::vector<Record> all_records;
    while (extractor.has_more_data()) {
        auto batch = extractor.extract_batch(10, context);
        auto records = batch.getRecords();
        all_records.insert(all_records.end(), records.begin(), records.end());
    }
    
    EXPECT_EQ(6, all_records.size());
}

// Tests CSV directory extractor
TEST_F(CsvExtractorTest, CsvDirectoryExtractor) {
    // Create multiple CSV files in directory
    createTestCsv("data1.csv", "id\n1\n2\n");
    createTestCsv("data2.csv", "id\n3\n4\n");
    createTestCsv("data3.txt", "id\n5\n6\n"); // Should be ignored
    
    CsvDirectoryExtractor extractor;
    std::unordered_map<std::string, std::any> config;
    config["directory"] = test_dir_.string();
    config["pattern"] = ".*\\.csv$";
    config["recursive"] = false;
    
    ProcessingContext context;
    extractor.initialize(config, context);
    
    // Count total records
    size_t total = 0;
    while (extractor.has_more_data()) {
        auto batch = extractor.extract_batch(10, context);
        total += batch.size();
    }
    
    EXPECT_EQ(4, total); // 2 records from each of 2 CSV files
}

// Tests compressed CSV extractor detection
TEST_F(CsvExtractorTest, CompressedCsvDetection) {
    CompressedCsvExtractor extractor;
    
    // Test file extension detection
    EXPECT_EQ(CompressedCsvExtractor::CompressionFormat::Gzip, 
              CompressedCsvExtractor::detect_compression("data.csv.gz"));
    EXPECT_EQ(CompressedCsvExtractor::CompressionFormat::Zip, 
              CompressedCsvExtractor::detect_compression("data.csv.zip"));
    EXPECT_EQ(CompressedCsvExtractor::CompressionFormat::Bzip2, 
              CompressedCsvExtractor::detect_compression("data.csv.bz2"));
    EXPECT_EQ(CompressedCsvExtractor::CompressionFormat::None, 
              CompressedCsvExtractor::detect_compression("data.csv"));
}

// Tests CSV factory
TEST_F(CsvExtractorTest, CsvExtractorFactory) {
    // Test creating different CSV extractor types
    auto csv_extractor = CsvExtractorFactory::create("csv");
    EXPECT_NE(nullptr, csv_extractor);
    EXPECT_EQ("csv", csv_extractor->get_type());
    
    auto multi_extractor = CsvExtractorFactory::create("multi_csv");
    EXPECT_NE(nullptr, multi_extractor);
    EXPECT_EQ("multi_csv", multi_extractor->get_type());
    
    auto dir_extractor = CsvExtractorFactory::create("csv_directory");
    EXPECT_NE(nullptr, dir_extractor);
    EXPECT_EQ("csv_directory", dir_extractor->get_type());
    
    auto compressed_extractor = CsvExtractorFactory::create("compressed_csv");
    EXPECT_NE(nullptr, compressed_extractor);
    EXPECT_EQ("compressed_csv", compressed_extractor->get_type());
    
    // Test invalid type
    EXPECT_THROW(CsvExtractorFactory::create("invalid_type"), ConfigurationException);
}