/**
 * @file connection_pool_test.cpp
 * @brief Unit tests for ConnectionPool and related classes
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "extract/database_connector.h"
#include "common/exceptions.h"
#include <thread>
#include <chrono>
#include <atomic>
#include <vector>

namespace omop::extract::test {

using namespace ::testing;
using namespace std::chrono_literals;

// Mock database connection for testing
class MockDatabaseConnection : public IDatabaseConnection {
public:
    MOCK_METHOD(void, connect, (const ConnectionParams& params), (override));
    MOCK_METHOD(void, disconnect, (), (override));
    MOCK_METHOD(bool, is_connected, (), (const, override));
    MOCK_METHOD(std::unique_ptr<IResultSet>, execute_query, (const std::string& sql), (override));
    MOCK_METHOD(size_t, execute_update, (const std::string& sql), (override));
    MOCK_METHOD(std::unique_ptr<IPreparedStatement>, prepare_statement, (const std::string& sql), (override));
    MOCK_METHOD(void, begin_transaction, (), (override));
    MOCK_METHOD(void, commit, (), (override));
    MOCK_METHOD(void, rollback, (), (override));
    MOCK_METHOD(std::string, get_database_type, (), (const, override));
    MOCK_METHOD(std::string, get_version, (), (const, override));
    MOCK_METHOD(void, set_query_timeout, (int seconds), (override));
    MOCK_METHOD(bool, table_exists, (const std::string& table_name, const std::string& schema), (const, override));
};

// Test fixture for ConnectionPool tests
class ConnectionPoolTest : public ::testing::Test {
protected:
    // Factory function for creating mock connections
    std::unique_ptr<IDatabaseConnection> CreateMockConnection() {
        auto conn = std::make_unique<MockDatabaseConnection>();
        EXPECT_CALL(*conn, is_connected())
            .WillRepeatedly(Return(true));
        return conn;
    }
    
    // Factory function that tracks creation count
    std::unique_ptr<IDatabaseConnection> CreateTrackedConnection() {
        creation_count_++;
        return CreateMockConnection();
    }
    
    std::atomic<int> creation_count_{0};
};

// Test basic connection pool creation and acquisition
TEST_F(ConnectionPoolTest, BasicPoolOperations) {
    ConnectionPool pool(2, 5, [this]() { return CreateMockConnection(); });
    
    // Acquire a connection
    auto conn1 = pool.acquire();
    EXPECT_NE(conn1, nullptr);
    EXPECT_TRUE(conn1->is_connected());
    
    // Get statistics
    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.active_connections, 1);
    EXPECT_GE(stats.total_connections, 2); // At least min_connections
    
    // Release connection
    pool.release(std::move(conn1));
    
    stats = pool.get_statistics();
    EXPECT_EQ(stats.active_connections, 0);
    EXPECT_GT(stats.idle_connections, 0);
}

// Test connection pool with min/max limits
TEST_F(ConnectionPoolTest, MinMaxConnectionLimits) {
    size_t min_conn = 2;
    size_t max_conn = 4;
    
    ConnectionPool pool(min_conn, max_conn, [this]() { return CreateTrackedConnection(); });
    
    // Initial creation should be min_connections
    EXPECT_EQ(creation_count_, min_conn);
    
    // Acquire all min connections
    std::vector<std::unique_ptr<IDatabaseConnection>> connections;
    for (size_t i = 0; i < min_conn; ++i) {
        connections.push_back(pool.acquire());
    }
    
    // Pool should create new connections up to max
    connections.push_back(pool.acquire());
    EXPECT_GT(creation_count_, min_conn);
    
    connections.push_back(pool.acquire());
    EXPECT_EQ(creation_count_, max_conn);
    
    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.active_connections, max_conn);
    EXPECT_EQ(stats.total_connections, max_conn);
}

// Test connection pool timeout
TEST_F(ConnectionPoolTest, AcquisitionTimeout) {
    ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });
    
    // Acquire the only connection
    auto conn1 = pool.acquire();
    
    // Try to acquire another with timeout
    auto start = std::chrono::steady_clock::now();
    EXPECT_THROW(pool.acquire(100), common::DatabaseException);
    auto end = std::chrono::steady_clock::now();
    
    // Verify timeout occurred
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    EXPECT_GE(duration.count(), 100);
    EXPECT_LT(duration.count(), 200); // Should not wait much longer than timeout
}

// Test concurrent access to connection pool
TEST_F(ConnectionPoolTest, ConcurrentAccess) {
    ConnectionPool pool(2, 10, [this]() { return CreateMockConnection(); });
    
    std::atomic<int> success_count{0};
    std::vector<std::thread> threads;
    
    // Launch multiple threads to acquire and release connections
    for (int i = 0; i < 20; ++i) {
        threads.emplace_back([&pool, &success_count]() {
            try {
                auto conn = pool.acquire(1000);
                if (conn) {
                    success_count++;
                    std::this_thread::sleep_for(10ms);
                    pool.release(std::move(conn));
                }
            } catch (...) {
                // Timeout is acceptable in this test
            }
        });
    }
    
    // Wait for all threads
    for (auto& t : threads) {
        t.join();
    }
    
    // At least some threads should have succeeded
    EXPECT_GT(success_count, 0);
    
    // Final statistics should be consistent
    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.active_connections, 0);
    EXPECT_EQ(stats.total_connections, stats.idle_connections);
}

// Test connection validation
TEST_F(ConnectionPoolTest, ConnectionValidation) {
    std::vector<std::unique_ptr<MockDatabaseConnection>> mock_connections;
    int conn_index = 0;
    
    auto factory = [&mock_connections, &conn_index]() {
        auto conn = std::make_unique<MockDatabaseConnection>();
        
        // First 2 connections are valid, 3rd is invalid, 4th is valid
        if (conn_index < 2 || conn_index == 3) {
            EXPECT_CALL(*conn, is_connected())
                .WillRepeatedly(Return(true));
        } else {
            EXPECT_CALL(*conn, is_connected())
                .WillOnce(Return(false))  // Invalid during validation
                .WillRepeatedly(Return(true));
        }
        
        conn_index++;
        mock_connections.push_back(std::unique_ptr<MockDatabaseConnection>(conn.get()));
        return std::unique_ptr<IDatabaseConnection>(conn.release());
    };
    
    ConnectionPool pool(3, 5, factory);
    
    // Let connections go idle
    std::this_thread::sleep_for(10ms);
    
    // Validate connections - should remove 1 invalid connection
    size_t invalid_count = pool.validate_connections();
    EXPECT_EQ(invalid_count, 1);
    
    // Pool should maintain minimum connections
    auto stats = pool.get_statistics();
    EXPECT_GE(stats.total_connections, 3);
}

// Test clear idle connections
TEST_F(ConnectionPoolTest, ClearIdleConnections) {
    ConnectionPool pool(2, 5, [this]() { return CreateMockConnection(); });
    
    // Initial state
    auto stats = pool.get_statistics();
    EXPECT_GT(stats.idle_connections, 0);
    
    // Clear idle connections
    pool.clear_idle_connections();
    
    stats = pool.get_statistics();
    EXPECT_EQ(stats.idle_connections, 0);
    
    // Pool should still work
    auto conn = pool.acquire();
    EXPECT_NE(conn, nullptr);
    pool.release(std::move(conn));
}

// Test pool statistics
TEST_F(ConnectionPoolTest, PoolStatistics) {
    ConnectionPool pool(1, 3, [this]() { return CreateMockConnection(); });
    
    // Initial statistics
    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.total_acquisitions, 0);
    EXPECT_EQ(stats.total_releases, 0);
    EXPECT_EQ(stats.wait_count, 0);
    
    // Acquire and release
    auto conn = pool.acquire();
    pool.release(std::move(conn));
    
    stats = pool.get_statistics();
    EXPECT_EQ(stats.total_acquisitions, 1);
    EXPECT_EQ(stats.total_releases, 1);
}

// Test invalid connection handling
TEST_F(ConnectionPoolTest, InvalidConnectionHandling) {
    auto factory = []() {
        auto conn = std::make_unique<MockDatabaseConnection>();
        EXPECT_CALL(*conn, is_connected())
            .WillOnce(Return(false))  // Invalid on first check
            .WillRepeatedly(Return(true));
        return std::move(conn);
    };
    
    ConnectionPool pool(1, 2, factory);
    
    // Acquire connection - should get a new valid one
    auto conn = pool.acquire();
    EXPECT_NE(conn, nullptr);
    EXPECT_TRUE(conn->is_connected());
}

// Test pool destruction with active connections
TEST_F(ConnectionPoolTest, DestructionWithActiveConnections) {
    auto pool = std::make_unique<ConnectionPool>(2, 5, [this]() { return CreateMockConnection(); });
    
    // Acquire connections but don't release
    auto conn1 = pool->acquire();
    auto conn2 = pool->acquire();
    
    // Destroy pool - should handle gracefully
    EXPECT_NO_THROW(pool.reset());
    
    // Connections should still be valid
    EXPECT_TRUE(conn1->is_connected());
    EXPECT_TRUE(conn2->is_connected());
}

// Test connection factory failure
TEST_F(ConnectionPoolTest, ConnectionFactoryFailure) {
    int creation_attempts = 0;
    auto factory = [&creation_attempts]() -> std::unique_ptr<IDatabaseConnection> {
        creation_attempts++;
        if (creation_attempts <= 2) {
            throw common::DatabaseException("Connection failed", "test", 0);
        }
        auto conn = std::make_unique<MockDatabaseConnection>();
        EXPECT_CALL(*conn, is_connected()).WillRepeatedly(Return(true));
        return conn;
    };
    
    // Pool should handle initial creation failures
    EXPECT_THROW(ConnectionPool(2, 5, factory), common::DatabaseException);
}

// Test wait time statistics
TEST_F(ConnectionPoolTest, WaitTimeStatistics) {
    ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });
    
    // Acquire the only connection
    auto conn1 = pool.acquire();
    
    // Start a thread that will wait
    std::thread waiter([&pool]() {
        std::this_thread::sleep_for(50ms);
        try {
            auto conn = pool.acquire(200);
        } catch (...) {
            // Expected timeout
        }
    });
    
    // Release connection after a delay
    std::this_thread::sleep_for(100ms);
    pool.release(std::move(conn1));
    
    waiter.join();
    
    auto stats = pool.get_statistics();
    EXPECT_GT(stats.wait_count, 0);
    EXPECT_GT(stats.avg_wait_time.count(), 0);
}

// Test that releasing null connection is safe
TEST_F(ConnectionPoolTest, ReleaseNullConnection) {
    ConnectionPool pool(1, 2, [this]() { return CreateMockConnection(); });
    
    // Release null connection should be safe
    EXPECT_NO_THROW(pool.release(nullptr));
    
    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.total_releases, 0); // Null release shouldn't count
}

// Test pool behavior when max_connections equals min_connections
TEST_F(ConnectionPoolTest, EqualMinMaxConnections) {
    ConnectionPool pool(3, 3, [this]() { return CreateMockConnection(); });
    
    auto stats = pool.get_statistics();
    EXPECT_EQ(stats.total_connections, 3);
    
    // Acquire all connections
    std::vector<std::unique_ptr<IDatabaseConnection>> connections;
    for (int i = 0; i < 3; ++i) {
        connections.push_back(pool.acquire());
    }
    
    // Next acquire should timeout
    EXPECT_THROW(pool.acquire(100), common::DatabaseException);
}

// Test infinite timeout (blocking acquire)
TEST_F(ConnectionPoolTest, InfiniteTimeout) {
    ConnectionPool pool(1, 1, [this]() { return CreateMockConnection(); });
    
    auto conn1 = pool.acquire();
    
    std::atomic<bool> acquired{false};
    std::thread acquirer([&pool, &acquired]() {
        auto conn = pool.acquire(-1); // Infinite timeout
        acquired = true;
        pool.release(std::move(conn));
    });
    
    // Give thread time to start waiting
    std::this_thread::sleep_for(50ms);
    EXPECT_FALSE(acquired);
    
    // Release connection
    pool.release(std::move(conn1));
    
    // Acquirer should succeed
    acquirer.join();
    EXPECT_TRUE(acquired);
}

} // namespace omop::extract::test
