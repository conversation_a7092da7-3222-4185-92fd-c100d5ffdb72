File tests/integration/test_api_integration.cpp
File tests/integration/cdm/test_schema_creation_integration.cpp
File tests/integration/cdm/test_table_definitions_integration.cpp
File tests/integration/cdm/test_omop_tables_integration.cpp
File tests/integration/CMakeLists.txt
File tests/integration/core/test_pipeline_integration.cpp
File tests/integration/core/test_record_batch_integration.cpp
File tests/integration/core/test_job_manager_integration.cpp
File tests/integration/core/test_job_scheduler_integration.cpp
File tests/integration/test_batch_workflow_integration.cpp
File tests/integration/test_complex_etl_workflow.cpp
File tests/integration/quality/test_data_profiling.cpp
File tests/integration/quality/CMakeLists.txt
File tests/integration/quality/test_anomaly_detection.cpp
File tests/integration/quality/test_data_lineage.cpp
File tests/integration/quality/test_quality_rules_engine.cpp
File tests/integration/config/test_configuration_management.cpp
File tests/integration/security/test_authentication_integration.cpp
File tests/integration/test_data/json/clinical_data.json
File tests/integration/test_data/json/patient_records.json
File tests/integration/test_data/csv/test_data_visits.csv
File tests/integration/test_data/csv/medications.csv
File tests/integration/test_data/csv/test_data_locations.csv
File tests/integration/test_data/csv/test_data_edge_cases.csv
File tests/integration/test_data/csv/conditions.csv
File tests/integration/test_data/csv/test_data_procedures.csv
File tests/integration/test_data/csv/test_data_observations.csv
File tests/integration/test_data/csv/test_data_vocabulary_mappings.csv
File tests/integration/test_data/csv/test_data_drug_exposures.csv
File tests/integration/test_data/csv/test_data_care_sites.csv
File tests/integration/test_data/csv/patients.csv
File tests/integration/test_data/csv/test_data_providers.csv
File tests/integration/test_data/yaml/test_config.yaml
File tests/integration/test_data/yaml/mapping_config.yaml
File tests/integration/test_data/sql/test_schema.sql
File tests/integration/test_data/sql/test_data.sql
File tests/integration/common/test_logging_integration.cpp
File tests/integration/common/test_utilities_integration.cpp
File tests/integration/common/test_validation_integration.cpp
File tests/integration/common/test_configuration_integration.cpp
File tests/integration/load/test_loader_strategies_integration.cpp
File tests/integration/load/test_parallel_loading_integration.cpp
File tests/integration/load/test_batch_inserter_integration.cpp
File tests/integration/load/test_database_loader_integration.cpp
File tests/integration/example_usage.cpp
File tests/integration/extract/test_csv_extractor_integration.cpp
File tests/integration/extract/test_multi_source_extraction_integration.cpp
File tests/integration/extract/test_json_extractor_integration.cpp
File tests/integration/extract/test_database_extractor_integration.cpp
File tests/integration/service/test_etl_service_integration.cpp
File tests/integration/monitoring/test_alerting_integration.cpp
File tests/integration/monitoring/CMakeLists.txt
File tests/integration/monitoring/test_logging_integration.cpp
File tests/integration/monitoring/test_metrics_collection.cpp
File tests/integration/monitoring/test_tracing_integration.cpp
File tests/integration/performance/test_concurrent_operations.cpp
File tests/integration/performance/CMakeLists.txt
File tests/integration/performance/test_load_performance.cpp
File tests/integration/performance/test_memory_usage.cpp
File tests/integration/performance/test_scalability.cpp
File tests/integration/e2e/test_full_pipeline_integration.cpp
File tests/integration/e2e/test_etl_service_integration.cpp
File tests/integration/e2e/test_multi_tenant_etl_e2e.cpp
File tests/integration/test_helpers/database_fixture.h
File tests/integration/test_helpers/test_data_generator.h
File tests/integration/test_helpers/database_fixture.cpp
File tests/integration/test_helpers/integration_test_base.h
File tests/integration/test_helpers/test_data_generator.cpp
File tests/integration/transform/test_complex_transformations_integration.cpp
File tests/integration/transform/test_transformation_engine_integration.cpp
File tests/integration/transform/test_field_transformations_integration.cpp
File tests/integration/transform/test_vocabulary_service_integration.cpp
File tests/integration/test_data_quality_validation.cpp
